#!/bin/bash

# VPS Email Server Installation Script
# Run this on your VPS to set up email sending infrastructure

echo "=== VPS Email Server Installation ==="
echo "This script will install and configure email sending on your VPS"
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Please run this script as root (use sudo)"
    exit 1
fi

# Update system
echo "Updating system packages..."
apt update && apt upgrade -y

# Install required packages
echo "Installing required packages..."
apt install -y python3 python3-pip postfix mailutils ufw fail2ban

# Install Python packages
echo "Installing Python packages..."
pip3 install flask sqlite3

# Configure firewall
echo "Configuring firewall..."
ufw allow 22    # SSH
ufw allow 25    # SMTP
ufw allow 587   # SMTP submission
ufw allow 5000  # Web dashboard
ufw --force enable

# Configure Postfix
echo "Configuring Postfix..."
read -p "Enter your domain name (e.g., yourdomain.com): " DOMAIN
read -p "Enter your VPS IP address: " VPS_IP

# Backup original config
cp /etc/postfix/main.cf /etc/postfix/main.cf.backup

# Create new Postfix configuration
cat > /etc/postfix/main.cf << EOF
# Basic configuration
myhostname = mail.$DOMAIN
mydomain = $DOMAIN
myorigin = \$mydomain
inet_interfaces = all
inet_protocols = ipv4
mydestination = \$myhostname, localhost.\$mydomain, localhost, \$mydomain

# Network configuration
mynetworks = *********/8 [::ffff:*********]/104 [::1]/128 $VPS_IP/32
relayhost = 

# Mail delivery
home_mailbox = Maildir/
mailbox_command = 

# Security
smtpd_banner = \$myhostname ESMTP
biff = no
append_dot_mydomain = no
readme_directory = no

# TLS configuration
smtpd_use_tls = yes
smtpd_tls_cert_file = /etc/ssl/certs/ssl-cert-snakeoil.pem
smtpd_tls_key_file = /etc/ssl/private/ssl-cert-snakeoil.key
smtpd_tls_security_level = may
smtp_tls_security_level = may
smtpd_tls_session_cache_database = btree:\${data_directory}/smtpd_scache
smtp_tls_session_cache_database = btree:\${data_directory}/smtp_scache

# SMTP restrictions
smtpd_relay_restrictions = permit_mynetworks permit_sasl_authenticated defer_unauth_destination
smtpd_recipient_restrictions = permit_mynetworks permit_sasl_authenticated reject_unauth_destination

# Message size limit (50MB)
message_size_limit = 52428800
mailbox_size_limit = 0
recipient_delimiter = +
EOF

# Restart Postfix
systemctl restart postfix
systemctl enable postfix

# Create email server directory
mkdir -p /opt/email-server
cd /opt/email-server

# Copy the Python email server script
cat > /opt/email-server/email_server.py << 'EOF'
# The Python script content goes here
# (This would be the content from vps_email_server.py)
EOF

# Create systemd service
cat > /etc/systemd/system/email-server.service << EOF
[Unit]
Description=VPS Email Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/email-server
ExecStart=/usr/bin/python3 /opt/email-server/email_server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
systemctl daemon-reload
systemctl enable email-server
systemctl start email-server

# Create DNS configuration guide
cat > /opt/email-server/dns_setup.txt << EOF
=== DNS CONFIGURATION REQUIRED ===

Add these DNS records to your domain:

1. A Record:
   mail.$DOMAIN    A    $VPS_IP

2. MX Record:
   $DOMAIN         MX   10 mail.$DOMAIN

3. SPF Record:
   $DOMAIN         TXT  "v=spf1 ip4:$VPS_IP ~all"

4. DMARC Record:
   _dmarc.$DOMAIN  TXT  "v=DMARC1; p=quarantine; rua=mailto:dmarc@$DOMAIN"

5. PTR Record (Contact your VPS provider):
   $VPS_IP         PTR  mail.$DOMAIN

6. DKIM Record (Generate with: opendkim-genkey -t -s default -d $DOMAIN):
   default._domainkey.$DOMAIN  TXT  "v=DKIM1; k=rsa; p=YOUR_PUBLIC_KEY"

=== TESTING ===

1. Test SMTP: telnet localhost 25
2. Send test email: echo "Test" | mail -s "Test Subject" <EMAIL>
3. Check logs: tail -f /var/log/mail.log
4. Web dashboard: http://$VPS_IP:5000

EOF

# Install DKIM
echo "Installing DKIM..."
apt install -y opendkim opendkim-tools

# Configure DKIM
mkdir -p /etc/opendkim/keys/$DOMAIN
cd /etc/opendkim/keys/$DOMAIN
opendkim-genkey -t -s default -d $DOMAIN

# Set permissions
chown opendkim:opendkim /etc/opendkim/keys/$DOMAIN/default.private
chmod 600 /etc/opendkim/keys/$DOMAIN/default.private

# Create DKIM configuration
cat > /etc/opendkim.conf << EOF
AutoRestart             Yes
AutoRestartRate         10/1h
UMask                   002
Syslog                  yes
SyslogSuccess           Yes
LogWhy                  Yes
Canonicalization        relaxed/simple
ExternalIgnoreList      refile:/etc/opendkim/TrustedHosts
InternalHosts           refile:/etc/opendkim/TrustedHosts
KeyTable                refile:/etc/opendkim/KeyTable
SigningTable            refile:/etc/opendkim/SigningTable
Mode                    sv
PidFile                 /var/run/opendkim/opendkim.pid
SignatureAlgorithm      rsa-sha256
UserID                  opendkim:opendkim
Socket                  inet:12301@localhost
EOF

# Create DKIM tables
echo "127.0.0.1" > /etc/opendkim/TrustedHosts
echo "localhost" >> /etc/opendkim/TrustedHosts
echo "$DOMAIN" >> /etc/opendkim/TrustedHosts
echo "mail.$DOMAIN" >> /etc/opendkim/TrustedHosts

echo "default._domainkey.$DOMAIN $DOMAIN:default:/etc/opendkim/keys/$DOMAIN/default.private" > /etc/opendkim/KeyTable
echo "*@$DOMAIN default._domainkey.$DOMAIN" > /etc/opendkim/SigningTable

# Configure Postfix to use DKIM
echo "" >> /etc/postfix/main.cf
echo "# DKIM configuration" >> /etc/postfix/main.cf
echo "milter_protocol = 2" >> /etc/postfix/main.cf
echo "milter_default_action = accept" >> /etc/postfix/main.cf
echo "smtpd_milters = inet:localhost:12301" >> /etc/postfix/main.cf
echo "non_smtpd_milters = inet:localhost:12301" >> /etc/postfix/main.cf

# Start DKIM
systemctl enable opendkim
systemctl start opendkim
systemctl restart postfix

# Create monitoring script
cat > /opt/email-server/monitor.sh << EOF
#!/bin/bash
# Email server monitoring script

echo "=== Email Server Status ==="
echo "Date: \$(date)"
echo ""

echo "Postfix Status:"
systemctl status postfix --no-pager -l

echo ""
echo "Email Server Status:"
systemctl status email-server --no-pager -l

echo ""
echo "DKIM Status:"
systemctl status opendkim --no-pager -l

echo ""
echo "Recent Mail Logs:"
tail -20 /var/log/mail.log

echo ""
echo "Queue Status:"
mailq

echo ""
echo "Disk Usage:"
df -h /

echo ""
echo "Memory Usage:"
free -h
EOF

chmod +x /opt/email-server/monitor.sh

# Create backup script
cat > /opt/email-server/backup.sh << EOF
#!/bin/bash
# Backup email server data

BACKUP_DIR="/opt/email-server/backups"
DATE=\$(date +%Y%m%d_%H%M%S)

mkdir -p \$BACKUP_DIR

# Backup database
cp /opt/email-server/email_campaigns.db \$BACKUP_DIR/email_campaigns_\$DATE.db

# Backup logs
cp /opt/email-server/email_server.log \$BACKUP_DIR/email_server_\$DATE.log

# Backup configuration
tar -czf \$BACKUP_DIR/config_\$DATE.tar.gz /etc/postfix/ /etc/opendkim/

echo "Backup completed: \$BACKUP_DIR"

# Keep only last 7 days of backups
find \$BACKUP_DIR -name "*.db" -mtime +7 -delete
find \$BACKUP_DIR -name "*.log" -mtime +7 -delete
find \$BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /opt/email-server/backup.sh

# Add cron job for daily backup
echo "0 2 * * * /opt/email-server/backup.sh" | crontab -

# Final setup
echo ""
echo "=== INSTALLATION COMPLETE ==="
echo ""
echo "Your VPS email server has been installed and configured!"
echo ""
echo "Next steps:"
echo "1. Configure DNS records (see /opt/email-server/dns_setup.txt)"
echo "2. Access web dashboard at: http://$VPS_IP:5000"
echo "3. Test email sending"
echo "4. Monitor logs: tail -f /var/log/mail.log"
echo ""
echo "Important files:"
echo "- Email server: /opt/email-server/email_server.py"
echo "- DNS setup guide: /opt/email-server/dns_setup.txt"
echo "- Monitor script: /opt/email-server/monitor.sh"
echo "- Backup script: /opt/email-server/backup.sh"
echo ""
echo "DKIM public key (add to DNS):"
cat /etc/opendkim/keys/$DOMAIN/default.txt
echo ""
echo "Remember to:"
echo "- Set up reverse DNS (PTR record) with your VPS provider"
echo "- Configure your domain's DNS records"
echo "- Test thoroughly before sending to real recipients"
echo "- Monitor your sender reputation"
echo ""
echo "Happy emailing! 🚀"
