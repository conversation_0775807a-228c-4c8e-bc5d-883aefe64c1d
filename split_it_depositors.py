#!/usr/bin/env python3
"""
IT Depositors <PERSON>
Specifically for: IT_DEPOSITORS_MARCO 9.9k.xlsx
Keeps only Name, Email, Phone columns and splits into 500-row files
"""

import pandas as pd
import os
import math
from pathlib import Path

def main():
    print("🚀 IT Depositors Marco CSV Splitter")
    print("=" * 50)
    
    # Target file
    excel_file = "IT_DEPOSITORS_MARCO 9.9k.xlsx"
    
    # Check if file exists
    if not os.path.exists(excel_file):
        print(f"❌ File not found: {excel_file}")
        print(f"\n📁 Current directory: {os.getcwd()}")
        print(f"📋 Files in current directory:")
        
        for file in os.listdir("."):
            if os.path.isfile(file):
                size = os.path.getsize(file) / 1024 / 1024  # MB
                print(f"   📄 {file} ({size:.1f} MB)")
            else:
                print(f"   📁 {file}/")
        
        print(f"\n💡 Please make sure '{excel_file}' is in this folder:")
        print(f"   {os.getcwd()}")
        input("\nPress Enter to exit...")
        return
    
    print(f"✅ Found file: {excel_file}")
    
    try:
        # Read Excel file
        print(f"📖 Reading Excel file...")
        df = pd.read_excel(excel_file)
        
        print(f"✅ File loaded successfully!")
        print(f"📊 Total rows: {len(df):,}")
        print(f"📊 Total columns: {len(df.columns)}")
        
        # Show all columns
        print(f"\n📋 Available columns:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        # Show sample data
        print(f"\n📖 First 3 rows:")
        print(df.head(3).to_string())
        
        # Find Name, Email, Phone columns
        name_col = None
        email_col = None
        phone_col = None
        
        # Auto-detect columns (case insensitive)
        for col in df.columns:
            col_lower = col.lower()
            
            # Look for Name column
            if not name_col and any(keyword in col_lower for keyword in ['name', 'nome', 'contact', 'first', 'last']):
                name_col = col
            
            # Look for Email column
            if not email_col and any(keyword in col_lower for keyword in ['email', 'mail', 'e-mail', 'posta']):
                email_col = col
            
            # Look for Phone column
            if not phone_col and any(keyword in col_lower for keyword in ['phone', 'tel', 'mobile', 'cell', 'number', 'telefono']):
                phone_col = col
        
        print(f"\n🔍 Auto-detected columns:")
        print(f"   Name: {name_col if name_col else 'NOT FOUND'}")
        print(f"   Email: {email_col if email_col else 'NOT FOUND'}")
        print(f"   Phone: {phone_col if phone_col else 'NOT FOUND'}")
        
        # Manual selection if auto-detection failed
        if not name_col:
            print(f"\n❓ Which column contains NAMES?")
            for i, col in enumerate(df.columns, 1):
                print(f"   {i}. {col}")
            while True:
                try:
                    choice = input("Select Name column number: ")
                    index = int(choice) - 1
                    if 0 <= index < len(df.columns):
                        name_col = df.columns[index]
                        break
                    else:
                        print("❌ Invalid choice!")
                except ValueError:
                    print("❌ Please enter a number!")
        
        if not email_col:
            print(f"\n❓ Which column contains EMAILS?")
            for i, col in enumerate(df.columns, 1):
                print(f"   {i}. {col}")
            while True:
                try:
                    choice = input("Select Email column number: ")
                    index = int(choice) - 1
                    if 0 <= index < len(df.columns):
                        email_col = df.columns[index]
                        break
                    else:
                        print("❌ Invalid choice!")
                except ValueError:
                    print("❌ Please enter a number!")
        
        if not phone_col:
            print(f"\n❓ Which column contains PHONE numbers?")
            for i, col in enumerate(df.columns, 1):
                print(f"   {i}. {col}")
            print(f"   0. Skip phone column (Name + Email only)")
            while True:
                try:
                    choice = input("Select Phone column number (or 0 to skip): ")
                    if choice == "0":
                        phone_col = None
                        break
                    index = int(choice) - 1
                    if 0 <= index < len(df.columns):
                        phone_col = df.columns[index]
                        break
                    else:
                        print("❌ Invalid choice!")
                except ValueError:
                    print("❌ Please enter a number!")
        
        # Create filtered dataframe
        print(f"\n🧹 Processing data...")
        
        if phone_col:
            df_filtered = df[[name_col, email_col, phone_col]].copy()
            df_filtered.columns = ['Name', 'Email', 'Phone']
        else:
            df_filtered = df[[name_col, email_col]].copy()
            df_filtered.columns = ['Name', 'Email']
            df_filtered['Phone'] = ''  # Add empty phone column
        
        original_rows = len(df_filtered)
        
        # Clean data
        print(f"🧹 Cleaning data...")
        
        # Remove rows without email
        df_filtered = df_filtered.dropna(subset=['Email'])
        
        # Remove invalid emails (basic check)
        df_filtered = df_filtered[df_filtered['Email'].str.contains('@', na=False)]
        
        # Fill empty names and phones
        df_filtered['Name'] = df_filtered['Name'].fillna('')
        df_filtered['Phone'] = df_filtered['Phone'].fillna('')
        
        # Remove completely empty rows
        df_filtered = df_filtered.dropna(how='all')
        
        # Remove duplicates based on email
        df_filtered = df_filtered.drop_duplicates(subset=['Email'], keep='first')
        
        final_rows = len(df_filtered)
        removed_rows = original_rows - final_rows
        
        print(f"✅ Data processed:")
        print(f"   Original rows: {original_rows:,}")
        print(f"   Final rows: {final_rows:,}")
        print(f"   Removed rows: {removed_rows:,}")
        
        if final_rows == 0:
            print("❌ No valid data found!")
            input("Press Enter to exit...")
            return
        
        # Show sample of cleaned data
        print(f"\n📋 Sample cleaned data:")
        print(df_filtered.head(3).to_string(index=False))
        
        # Split into CSV files with 500 rows each
        rows_per_file = 500
        total_files = math.ceil(final_rows / rows_per_file)
        
        # Create output folder
        output_folder = "IT_DEPOSITORS_CSV"
        Path(output_folder).mkdir(exist_ok=True)
        
        print(f"\n📁 Creating {total_files} CSV files with {rows_per_file} rows each...")
        print(f"📁 Output folder: {output_folder}/")
        
        for i in range(total_files):
            start_row = i * rows_per_file
            end_row = min((i + 1) * rows_per_file, final_rows)
            
            chunk = df_filtered.iloc[start_row:end_row]
            
            filename = f"IT_DEPOSITORS_part_{i+1:03d}_of_{total_files:03d}.csv"
            filepath = os.path.join(output_folder, filename)
            
            chunk.to_csv(filepath, index=False)
            
            print(f"✅ {filename} ({len(chunk)} rows)")
        
        # Create summary file
        summary_file = os.path.join(output_folder, "SUMMARY.txt")
        with open(summary_file, 'w') as f:
            f.write("IT Depositors Marco - Email Campaign Files\n")
            f.write("=" * 45 + "\n\n")
            f.write(f"Source file: {excel_file}\n")
            f.write(f"Total contacts: {final_rows:,}\n")
            f.write(f"Files created: {total_files}\n")
            f.write(f"Rows per file: {rows_per_file}\n")
            f.write(f"Columns: Name, Email, Phone\n\n")
            f.write("Original columns used:\n")
            f.write(f"  Name: {name_col}\n")
            f.write(f"  Email: {email_col}\n")
            f.write(f"  Phone: {phone_col if phone_col else 'Not used'}\n\n")
            f.write("Data cleaning performed:\n")
            f.write(f"  • Removed rows without valid emails\n")
            f.write(f"  • Removed duplicate emails\n")
            f.write(f"  • Filled empty names and phones\n\n")
            f.write("Files created:\n")
            for i in range(total_files):
                filename = f"IT_DEPOSITORS_part_{i+1:03d}_of_{total_files:03d}.csv"
                f.write(f"  • {filename}\n")
        
        # Create a sample file for testing
        if final_rows > 0:
            sample_file = os.path.join(output_folder, "SAMPLE_10_contacts.csv")
            sample_data = df_filtered.head(10)
            sample_data.to_csv(sample_file, index=False)
            print(f"📄 Created sample file: SAMPLE_10_contacts.csv")
        
        print(f"\n🎉 SUCCESS! Created {total_files} CSV files!")
        print(f"📁 Location: {output_folder}/")
        print(f"📄 Summary: {summary_file}")
        print(f"\n📧 Perfect for MailSuite email campaigns!")
        print(f"💡 Each file has exactly Name, Email, Phone columns with max {rows_per_file} rows.")
        print(f"🚀 Upload these files to MailSuite for bulk email sending!")
        
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        print(f"💡 Make sure the file is not open in Excel and try again.")
        import traceback
        print(f"Debug info: {traceback.format_exc()}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
