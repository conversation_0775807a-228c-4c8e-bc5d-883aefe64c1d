#!/usr/bin/env python3
"""
Simple script to create basic icons for the Chrome extension
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, filename):
    # Create a new image with a blue background
    img = Image.new('RGBA', (size, size), (26, 115, 232, 255))  # Gmail blue
    draw = ImageDraw.Draw(img)
    
    # Draw a simple envelope icon
    margin = size // 8
    envelope_width = size - 2 * margin
    envelope_height = int(envelope_width * 0.7)
    
    # Calculate position to center the envelope
    x = margin
    y = (size - envelope_height) // 2
    
    # Draw envelope body (white)
    draw.rectangle([x, y, x + envelope_width, y + envelope_height], 
                  fill=(255, 255, 255, 255), outline=(200, 200, 200, 255))
    
    # Draw envelope flap
    flap_height = envelope_height // 3
    points = [
        (x, y),
        (x + envelope_width // 2, y + flap_height),
        (x + envelope_width, y),
        (x + envelope_width, y + flap_height),
        (x, y + flap_height)
    ]
    draw.polygon(points, fill=(26, 115, 232, 255))
    
    # Add "M" for Mail
    if size >= 48:
        try:
            font_size = size // 4
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
        
        text = "M"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        text_x = x + (envelope_width - text_width) // 2
        text_y = y + envelope_height // 2 - text_height // 2
        
        draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
    
    # Save the image
    img.save(filename, 'PNG')
    print(f"Created {filename} ({size}x{size})")

def main():
    # Create images directory if it doesn't exist
    os.makedirs('images', exist_ok=True)
    
    # Create icons in different sizes
    create_icon(16, 'images/icon16.png')
    create_icon(48, 'images/icon48.png')
    create_icon(128, 'images/icon128.png')
    
    print("All icons created successfully!")

if __name__ == "__main__":
    main()
