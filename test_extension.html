<!DOCTYPE html>
<html>
<head>
    <title>Extension Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>MailSuite Extension Test</h1>
    
    <div class="section">
        <h2>Extension Installation Test</h2>
        <p>1. Load the extension in Chrome (chrome://extensions/)</p>
        <p>2. Enable Developer mode</p>
        <p>3. Click "Load unpacked" and select the extension folder</p>
        <p>4. Go to Gmail and look for the "MailSuite" button</p>
    </div>
    
    <div class="section">
        <h2>VPS Email Server Test</h2>
        <p>Test your VPS email server:</p>
        <button onclick="testVPSConnection()">Test VPS Connection</button>
        <button onclick="sendTestEmail()">Send Test Email</button>
        <div id="vps-results"></div>
    </div>
    
    <div class="section">
        <h2>CSV Test Data</h2>
        <p>Use this sample CSV data to test the extension:</p>
        <textarea id="sample-csv" rows="10" cols="50">firstName,lastName,email,company
John,Doe,<EMAIL>,Acme Corp
Jane,Smith,<EMAIL>,Tech Inc
Bob,Johnson,<EMAIL>,Innovation LLC
Alice,Brown,<EMAIL>,Global Solutions
Mike,Wilson,<EMAIL>,Big Company</textarea>
        <br>
        <button onclick="downloadCSV()">Download Sample CSV</button>
    </div>
    
    <div class="section">
        <h2>Quick Setup Guide</h2>
        <ol>
            <li><strong>Install Extension:</strong> Load the extension in Chrome</li>
            <li><strong>Set up VPS:</strong> Run the installation script on your VPS</li>
            <li><strong>Configure DNS:</strong> Add required DNS records</li>
            <li><strong>Test Email:</strong> Send a test email through the VPS</li>
            <li><strong>Start Campaign:</strong> Use the extension to send bulk emails</li>
        </ol>
    </div>
    
    <script>
        function testVPSConnection() {
            const vpsUrl = prompt("Enter your VPS URL (e.g., http://YOUR_VPS_IP:5000):");
            if (!vpsUrl) return;
            
            fetch(vpsUrl + '/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('vps-results').innerHTML = 
                        `<div class="success">✓ VPS Connected! Queue: ${data.queue_size}, Status: ${data.is_running ? 'Running' : 'Stopped'}</div>`;
                })
                .catch(error => {
                    document.getElementById('vps-results').innerHTML = 
                        `<div class="error">✗ Connection failed: ${error.message}</div>`;
                });
        }
        
        function sendTestEmail() {
            const vpsUrl = prompt("Enter your VPS URL (e.g., http://YOUR_VPS_IP:5000):");
            if (!vpsUrl) return;
            
            const testEmail = prompt("Enter test email address:");
            if (!testEmail) return;
            
            const emailData = {
                to_email: testEmail,
                subject: "Test Email from VPS",
                body: "<h1>Test Email</h1><p>This is a test email sent from your VPS email server!</p>"
            };
            
            fetch(vpsUrl + '/send-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(emailData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('vps-results').innerHTML = 
                    `<div class="success">✓ Test email queued! Queue size: ${data.queue_size}</div>`;
            })
            .catch(error => {
                document.getElementById('vps-results').innerHTML = 
                    `<div class="error">✗ Failed to send: ${error.message}</div>`;
            });
        }
        
        function downloadCSV() {
            const csvContent = document.getElementById('sample-csv').value;
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'sample_contacts.csv';
            a.click();
            
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
