@echo off
echo 🚀 Excel to CSV Splitter
echo ========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo 💡 Please install Python from https://python.org
    echo    Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python is installed
echo.

REM Install required packages
echo 📦 Installing required packages...
pip install pandas openpyxl xlrd
echo.

REM Check if debug script exists
if not exist "debug_excel_split.py" (
    echo ❌ debug_excel_split.py not found!
    echo Please make sure the Python script is in the same folder.
    echo.
    pause
    exit /b 1
)

REM Run the debug script
echo 🔄 Running Excel splitter...
echo.
python debug_excel_split.py

echo.
echo ✅ Script completed!
echo.
pause
