// MailSuite Background Script
class MailSuiteBackground {
  constructor() {
    this.activeCampaigns = new Map();
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Listen for messages from content script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep the message channel open for async responses
    });

    // Listen for alarms (scheduled campaigns)
    chrome.alarms.onAlarm.addListener((alarm) => {
      this.handleAlarm(alarm);
    });

    // Listen for extension startup
    chrome.runtime.onStartup.addListener(() => {
      this.restoreActiveCampaigns();
    });

    chrome.runtime.onInstalled.addListener(() => {
      this.initializeExtension();
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'startCampaign':
          await this.startCampaign(message.campaign);
          sendResponse({ success: true });
          break;

        case 'scheduleCampaign':
          await this.scheduleCampaign(message.campaign);
          sendResponse({ success: true });
          break;

        case 'pauseCampaign':
          await this.pauseCampaign(message.campaignId);
          sendResponse({ success: true });
          break;

        case 'stopCampaign':
          await this.stopCampaign(message.campaignId);
          sendResponse({ success: true });
          break;

        case 'getCampaignStatus':
          const status = await this.getCampaignStatus(message.campaignId);
          sendResponse({ status });
          break;

        case 'updateProgress':
          await this.updateCampaignProgress(message.campaignId, message.progress);
          sendResponse({ success: true });
          break;

        case 'trackOpen':
          await this.trackEmailOpen(message.campaignId, message.recipientId);
          sendResponse({ success: true });
          break;

        case 'trackClick':
          await this.trackEmailClick(message.campaignId, message.recipientId, message.url);
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message });
    }
  }

  async startCampaign(campaign) {
    console.log('Starting campaign:', campaign.id);
    
    // Store campaign in active campaigns
    this.activeCampaigns.set(campaign.id, {
      ...campaign,
      status: 'active',
      startedAt: new Date().toISOString(),
      currentIndex: 0,
      sentCount: 0,
      openCount: 0,
      clickCount: 0
    });

    // Save to storage
    await this.saveCampaignToStorage(campaign.id);

    // Start sending process
    await this.processCampaignBatch(campaign.id);
  }

  async scheduleCampaign(campaign) {
    console.log('Scheduling campaign:', campaign.id, 'for', campaign.scheduledFor);
    
    // Calculate delay until scheduled time
    const scheduledTime = new Date(campaign.scheduledFor).getTime();
    const now = Date.now();
    const delay = scheduledTime - now;

    if (delay <= 0) {
      // Schedule for immediate execution
      await this.startCampaign(campaign);
    } else {
      // Create alarm for future execution
      const alarmName = `campaign_${campaign.id}`;
      chrome.alarms.create(alarmName, { when: scheduledTime });
      
      // Store campaign for later execution
      await chrome.storage.local.set({
        [`scheduled_${campaign.id}`]: campaign
      });
    }
  }

  async handleAlarm(alarm) {
    if (alarm.name.startsWith('campaign_')) {
      const campaignId = alarm.name.replace('campaign_', '');
      
      // Get scheduled campaign
      const result = await chrome.storage.local.get([`scheduled_${campaignId}`]);
      const campaign = result[`scheduled_${campaignId}`];
      
      if (campaign) {
        // Start the campaign
        await this.startCampaign(campaign);
        
        // Clean up scheduled campaign
        await chrome.storage.local.remove([`scheduled_${campaignId}`]);
      }
    }
  }

  async processCampaignBatch(campaignId) {
    const campaign = this.activeCampaigns.get(campaignId);
    if (!campaign || campaign.status !== 'active') return;

    const batchSize = 20; // Send 20 emails per batch
    const delay = 60000; // 1 minute delay between batches
    
    const startIndex = campaign.currentIndex;
    const endIndex = Math.min(startIndex + batchSize, campaign.contacts.length);

    if (startIndex >= campaign.contacts.length) {
      // Campaign complete
      await this.completeCampaign(campaignId);
      return;
    }

    // Open Gmail compose windows for this batch
    for (let i = startIndex; i < endIndex; i++) {
      const contact = campaign.contacts[i];
      
      // Create a new tab for each email
      chrome.tabs.create({
        url: 'https://mail.google.com/mail/u/0/#inbox?compose=new',
        active: false
      }, (tab) => {
        // Store the contact info for this tab
        chrome.storage.local.set({
          [`tab_${tab.id}_contact`]: contact,
          [`tab_${tab.id}_campaign`]: campaignId,
          [`tab_${tab.id}_index`]: i
        });
      });

      // Small delay between opening tabs
      await this.sleep(2000);
    }

    // Update campaign progress
    campaign.currentIndex = endIndex;
    await this.saveCampaignToStorage(campaignId);

    // Schedule next batch if there are more emails
    if (endIndex < campaign.contacts.length) {
      setTimeout(() => {
        this.processCampaignBatch(campaignId);
      }, delay);
    }
  }

  async completeCampaign(campaignId) {
    const campaign = this.activeCampaigns.get(campaignId);
    if (!campaign) return;

    campaign.status = 'completed';
    campaign.completedAt = new Date().toISOString();

    await this.saveCampaignToStorage(campaignId);
    
    // Notify user
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'images/icon48.png',
      title: 'MailSuite Campaign Complete',
      message: `Campaign "${campaign.subject}" has been completed. ${campaign.sentCount} emails sent.`
    });

    console.log('Campaign completed:', campaignId);
  }

  async pauseCampaign(campaignId) {
    const campaign = this.activeCampaigns.get(campaignId);
    if (!campaign) return;

    campaign.status = 'paused';
    await this.saveCampaignToStorage(campaignId);
  }

  async stopCampaign(campaignId) {
    const campaign = this.activeCampaigns.get(campaignId);
    if (!campaign) return;

    campaign.status = 'stopped';
    campaign.stoppedAt = new Date().toISOString();
    
    await this.saveCampaignToStorage(campaignId);
    
    // Remove from active campaigns
    this.activeCampaigns.delete(campaignId);
  }

  async getCampaignStatus(campaignId) {
    const campaign = this.activeCampaigns.get(campaignId);
    return campaign ? campaign.status : 'not_found';
  }

  async updateCampaignProgress(campaignId, progress) {
    const campaign = this.activeCampaigns.get(campaignId);
    if (!campaign) return;

    campaign.sentCount = progress.sent || campaign.sentCount;
    campaign.currentIndex = progress.currentIndex || campaign.currentIndex;
    
    await this.saveCampaignToStorage(campaignId);
    
    // Broadcast progress to all tabs
    const tabs = await chrome.tabs.query({ url: 'https://mail.google.com/*' });
    tabs.forEach(tab => {
      chrome.tabs.sendMessage(tab.id, {
        type: 'campaignProgress',
        campaignId,
        progress: {
          sent: campaign.sentCount,
          total: campaign.contacts.length,
          currentIndex: campaign.currentIndex
        }
      }).catch(() => {}); // Ignore errors for inactive tabs
    });
  }

  async trackEmailOpen(campaignId, recipientId) {
    const campaign = this.activeCampaigns.get(campaignId);
    if (!campaign) return;

    campaign.openCount = (campaign.openCount || 0) + 1;
    
    // Store individual open event
    const openEvent = {
      campaignId,
      recipientId,
      timestamp: new Date().toISOString(),
      type: 'open'
    };

    await this.storeTrackingEvent(openEvent);
    await this.saveCampaignToStorage(campaignId);
  }

  async trackEmailClick(campaignId, recipientId, url) {
    const campaign = this.activeCampaigns.get(campaignId);
    if (!campaign) return;

    campaign.clickCount = (campaign.clickCount || 0) + 1;
    
    // Store individual click event
    const clickEvent = {
      campaignId,
      recipientId,
      url,
      timestamp: new Date().toISOString(),
      type: 'click'
    };

    await this.storeTrackingEvent(clickEvent);
    await this.saveCampaignToStorage(campaignId);
  }

  async storeTrackingEvent(event) {
    const result = await chrome.storage.local.get(['trackingEvents']);
    const events = result.trackingEvents || [];
    events.push(event);
    
    // Keep only last 10000 events to prevent storage bloat
    if (events.length > 10000) {
      events.splice(0, events.length - 10000);
    }
    
    await chrome.storage.local.set({ trackingEvents: events });
  }

  async saveCampaignToStorage(campaignId) {
    const campaign = this.activeCampaigns.get(campaignId);
    if (!campaign) return;

    // Get existing campaigns
    const result = await chrome.storage.local.get(['campaigns']);
    const campaigns = result.campaigns || {};
    
    campaigns[campaignId] = campaign;
    
    await chrome.storage.local.set({ campaigns });
  }

  async restoreActiveCampaigns() {
    const result = await chrome.storage.local.get(['campaigns']);
    const campaigns = result.campaigns || {};
    
    Object.values(campaigns).forEach(campaign => {
      if (campaign.status === 'active') {
        this.activeCampaigns.set(campaign.id, campaign);
        
        // Resume campaign processing
        this.processCampaignBatch(campaign.id);
      }
    });
  }

  async initializeExtension() {
    console.log('MailSuite extension initialized');
    
    // Set up default settings
    await chrome.storage.local.set({
      mailsuiteSettings: {
        defaultDelay: 60000, // 1 minute
        defaultBatchSize: 20,
        trackingEnabled: true,
        subdomainRotation: false
      }
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Initialize the background script
new MailSuiteBackground();
