#!/usr/bin/env python3
"""
Super Simple Excel Splitter - No fancy features, just works!
"""

import os

def main():
    print("🚀 Simple Excel Splitter")
    print("=" * 30)
    
    # Check if we can import pandas
    try:
        import pandas as pd
        print("✅ Pandas is available")
    except ImportError:
        print("❌ Pandas not installed!")
        print("Installing pandas...")
        os.system("pip install pandas openpyxl")
        try:
            import pandas as pd
            print("✅ Pandas installed successfully")
        except ImportError:
            print("❌ Failed to install pandas")
            input("Press Enter to exit...")
            return
    
    # Look for Excel files
    excel_files = []
    for file in os.listdir("."):
        if file.endswith(('.xlsx', '.xls')):
            excel_files.append(file)
    
    print(f"\n📁 Current folder: {os.getcwd()}")
    print(f"📋 Files found:")
    for file in os.listdir("."):
        print(f"   • {file}")
    
    if not excel_files:
        print("\n❌ No Excel files found!")
        input("Press Enter to exit...")
        return
    
    # Use first Excel file found
    excel_file = excel_files[0]
    print(f"\n📊 Processing: {excel_file}")
    
    try:
        # Read Excel file
        df = pd.read_excel(excel_file)
        print(f"✅ File loaded: {len(df)} rows, {len(df.columns)} columns")
        
        # Create output folder
        output_folder = "csv_splits"
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        
        # Split into files of 200 rows each
        rows_per_file = 200
        total_files = (len(df) + rows_per_file - 1) // rows_per_file  # Ceiling division
        
        print(f"📁 Creating {total_files} CSV files...")
        
        for i in range(total_files):
            start = i * rows_per_file
            end = min((i + 1) * rows_per_file, len(df))
            
            chunk = df.iloc[start:end]
            filename = f"part_{i+1:03d}.csv"
            filepath = os.path.join(output_folder, filename)
            
            chunk.to_csv(filepath, index=False)
            print(f"✅ Created {filename} ({len(chunk)} rows)")
        
        print(f"\n🎉 Success! Created {total_files} CSV files in '{output_folder}' folder")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
