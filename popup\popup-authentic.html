<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 380px;
      height: 600px;
      margin: 0;
      font-family: 'Google Sans', 'Segoe UI', Roboto, Arial, sans-serif;
      background: #ffffff;
      overflow: hidden;
    }
    
    .header {
      background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
      color: white;
      padding: 20px;
      text-align: center;
      position: relative;
    }
    
    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }
    
    .header-content {
      position: relative;
      z-index: 1;
    }
    
    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
    }
    
    .logo-icon {
      width: 32px;
      height: 32px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      font-size: 16px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      letter-spacing: 0.5px;
    }
    
    .header p {
      margin: 4px 0 0 0;
      font-size: 13px;
      opacity: 0.9;
      font-weight: 400;
    }
    
    .content {
      padding: 0;
      height: calc(100% - 120px);
      overflow-y: auto;
    }
    
    .section {
      padding: 16px 20px;
      border-bottom: 1px solid #f1f3f4;
    }
    
    .section:last-child {
      border-bottom: none;
    }
    
    .section h3 {
      margin: 0 0 12px 0;
      color: #202124;
      font-size: 15px;
      font-weight: 500;
      display: flex;
      align-items: center;
    }
    
    .section-icon {
      margin-right: 8px;
      font-size: 16px;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      margin-bottom: 16px;
    }
    
    .stat-card {
      background: #f8f9fa;
      border: 1px solid #e8eaed;
      border-radius: 8px;
      padding: 12px;
      text-align: center;
      transition: all 0.2s ease;
    }
    
    .stat-card:hover {
      background: #e8f0fe;
      border-color: #4285f4;
    }
    
    .stat-number {
      font-size: 18px;
      font-weight: 500;
      color: #4285f4;
      margin-bottom: 4px;
    }
    
    .stat-label {
      font-size: 11px;
      color: #5f6368;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-weight: 500;
    }
    
    .campaign-status {
      background: #f8f9fa;
      border: 1px solid #e8eaed;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .campaign-status.active {
      background: #e8f5e8;
      border-color: #34a853;
      color: #137333;
    }
    
    .campaign-status.inactive {
      background: #fef7e0;
      border-color: #fbbc04;
      color: #ea8600;
    }
    
    .status-text {
      font-size: 13px;
      font-weight: 500;
    }
    
    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #5f6368;
    }
    
    .status-indicator.active {
      background: #34a853;
      animation: pulse 2s infinite;
    }
    
    .status-indicator.inactive {
      background: #fbbc04;
    }
    
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    
    .button {
      width: 100%;
      padding: 12px 16px;
      background: #4285f4;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      margin-bottom: 8px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    
    .button:hover {
      background: #3367d6;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
    }
    
    .button:active {
      transform: translateY(0);
    }
    
    .button.secondary {
      background: #f8f9fa;
      color: #3c4043;
      border: 1px solid #dadce0;
    }
    
    .button.secondary:hover {
      background: #e8f0fe;
      border-color: #4285f4;
      color: #1a73e8;
    }
    
    .button.danger {
      background: #ea4335;
    }
    
    .button.danger:hover {
      background: #d33b2c;
    }
    
    .button-icon {
      font-size: 16px;
    }
    
    .activity-list {
      max-height: 120px;
      overflow-y: auto;
    }
    
    .activity-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f1f3f4;
      font-size: 13px;
    }
    
    .activity-item:last-child {
      border-bottom: none;
    }
    
    .activity-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #e8f0fe;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-size: 12px;
      color: #4285f4;
    }
    
    .activity-text {
      flex: 1;
      color: #3c4043;
    }
    
    .activity-time {
      font-size: 11px;
      color: #5f6368;
    }
    
    .no-activity {
      text-align: center;
      color: #5f6368;
      font-style: italic;
      padding: 20px 0;
      font-size: 13px;
    }
    
    .progress-bar {
      width: 100%;
      height: 4px;
      background: #e8eaed;
      border-radius: 2px;
      overflow: hidden;
      margin: 8px 0;
    }
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #4285f4, #34a853);
      border-radius: 2px;
      transition: width 0.3s ease;
      width: 0%;
    }
    
    .version-info {
      text-align: center;
      padding: 12px;
      font-size: 11px;
      color: #9aa0a6;
      border-top: 1px solid #f1f3f4;
    }
    
    /* Scrollbar */
    ::-webkit-scrollbar {
      width: 6px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f3f4;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #dadce0;
      border-radius: 3px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #bdc1c6;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="header-content">
      <div class="logo">
        <div class="logo-icon">📧</div>
        <div>
          <h1>MailSuite</h1>
          <p>Professional Email Automation</p>
        </div>
      </div>
    </div>
  </div>
  
  <div class="content">
    <div class="section">
      <h3><span class="section-icon">📊</span>Campaign Overview</h3>
      
      <div id="campaign-status" class="campaign-status inactive">
        <div class="status-text">No active campaigns</div>
        <div class="status-indicator inactive"></div>
      </div>
      
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-number" id="emails-sent">0</div>
          <div class="stat-label">Sent</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="emails-pending">0</div>
          <div class="stat-label">Queue</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="open-rate">0%</div>
          <div class="stat-label">Opens</div>
        </div>
      </div>
      
      <div class="progress-bar">
        <div class="progress-fill" id="campaign-progress"></div>
      </div>
    </div>
    
    <div class="section">
      <h3><span class="section-icon">🚀</span>Quick Actions</h3>
      <button class="button" id="open-gmail">
        <span class="button-icon">📧</span>
        Open Gmail
      </button>
      <button class="button secondary" id="new-campaign">
        <span class="button-icon">➕</span>
        New Campaign
      </button>
      <button class="button secondary" id="view-analytics">
        <span class="button-icon">📈</span>
        View Analytics
      </button>
    </div>
    
    <div class="section">
      <h3><span class="section-icon">⚙️</span>Management</h3>
      <button class="button secondary" id="settings">
        <span class="button-icon">⚙️</span>
        Settings
      </button>
      <button class="button secondary" id="export-data">
        <span class="button-icon">📥</span>
        Export Data
      </button>
      <button class="button danger" id="stop-campaign" style="display: none;">
        <span class="button-icon">⏹️</span>
        Stop Campaign
      </button>
    </div>
    
    <div class="section">
      <h3><span class="section-icon">📈</span>Recent Activity</h3>
      <div class="activity-list" id="recent-activity">
        <div class="no-activity">No recent activity</div>
      </div>
    </div>
  </div>
  
  <div class="version-info">
    MailSuite v1.0 • Professional Edition
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
