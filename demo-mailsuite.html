<!DOCTYPE html>
<html>
<head>
    <title>MailSuite Clone Demo</title>
    <style>
        body { 
            font-family: 'Google Sans', Arial, sans-serif; 
            margin: 40px; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #4285f4, #34a853); 
            color: white; 
            padding: 40px; 
            text-align: center; 
        }
        .header h1 { 
            margin: 0; 
            font-size: 32px; 
            font-weight: 500; 
        }
        .header p { 
            margin: 10px 0 0 0; 
            font-size: 16px; 
            opacity: 0.9; 
        }
        .content { 
            padding: 40px; 
        }
        .section { 
            margin: 30px 0; 
            padding: 30px; 
            border: 1px solid #e8eaed; 
            border-radius: 8px; 
            background: #fafbfc;
        }
        .section h2 { 
            margin: 0 0 20px 0; 
            color: #202124; 
            font-size: 20px;
            display: flex;
            align-items: center;
        }
        .section-icon {
            margin-right: 12px;
            font-size: 24px;
        }
        .features-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin: 20px 0; 
        }
        .feature-card { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            border: 1px solid #dadce0;
            transition: all 0.2s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .feature-card h3 { 
            margin: 0 0 10px 0; 
            color: #4285f4; 
            font-size: 16px;
        }
        .feature-card p { 
            margin: 0; 
            color: #5f6368; 
            line-height: 1.5;
        }
        .button { 
            padding: 12px 24px; 
            background: #4285f4; 
            color: white; 
            border: none; 
            border-radius: 6px; 
            font-size: 14px; 
            font-weight: 500;
            cursor: pointer; 
            margin: 10px 10px 10px 0; 
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover { 
            background: #3367d6; 
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
        }
        .button.secondary { 
            background: #f8f9fa; 
            color: #3c4043; 
            border: 1px solid #dadce0; 
        }
        .button.secondary:hover { 
            background: #e8f0fe; 
            border-color: #4285f4; 
            color: #1a73e8;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e8eaed;
        }
        .comparison-table th {
            background: #f8f9fa;
            font-weight: 500;
            color: #202124;
        }
        .comparison-table .check {
            color: #34a853;
            font-weight: bold;
        }
        .comparison-table .cross {
            color: #ea4335;
            font-weight: bold;
        }
        .steps {
            counter-reset: step-counter;
        }
        .step {
            counter-increment: step-counter;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
            position: relative;
        }
        .step::before {
            content: counter(step-counter);
            position: absolute;
            left: -12px;
            top: 20px;
            background: #4285f4;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #202124;
        }
        .step p {
            margin: 0;
            color: #5f6368;
            line-height: 1.5;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e8eaed;
            border-radius: 4px;
            padding: 16px;
            font-family: 'Roboto Mono', monospace;
            font-size: 13px;
            color: #202124;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            background: #fff3cd;
            padding: 16px;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        .highlight strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 MailSuite Clone</h1>
            <p>Professional Gmail Automation Extension - Authentic MailSuite Experience</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2><span class="section-icon">🎯</span>What You Get</h2>
                <p>This is a complete, authentic replica of the MailSuite extension with all the professional features you need for email automation.</p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <h3>📊 Mail Merge</h3>
                        <p>Upload CSV files and send personalized emails to thousands of recipients with custom merge tags like {{firstName}}, {{company}}, etc.</p>
                    </div>
                    <div class="feature-card">
                        <h3>📈 Email Tracking</h3>
                        <p>Track email opens, link clicks, and engagement metrics with detailed analytics dashboard.</p>
                    </div>
                    <div class="feature-card">
                        <h3>📝 Templates</h3>
                        <p>Save and reuse email templates for consistent messaging across campaigns.</p>
                    </div>
                    <div class="feature-card">
                        <h3>⏰ Scheduling</h3>
                        <p>Schedule campaigns to send at optimal times across different time zones.</p>
                    </div>
                    <div class="feature-card">
                        <h3>🔄 Subdomain Rotation</h3>
                        <p>Use multiple subdomains to improve deliverability and avoid spam filters.</p>
                    </div>
                    <div class="feature-card">
                        <h3>🎨 Authentic UI</h3>
                        <p>Exact visual replica of MailSuite with Google Material Design styling.</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2><span class="section-icon">⚡</span>MailSuite vs Gmail Accounts</h2>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Feature</th>
                            <th>MailSuite Clone</th>
                            <th>Multiple Gmail Accounts</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Daily Email Limit</td>
                            <td class="check">Unlimited*</td>
                            <td class="cross">500 per account</td>
                        </tr>
                        <tr>
                            <td>Setup Time</td>
                            <td class="check">5 minutes</td>
                            <td class="cross">Hours (20+ accounts)</td>
                        </tr>
                        <tr>
                            <td>Professional Appearance</td>
                            <td class="check">Your domain</td>
                            <td class="cross">Gmail addresses</td>
                        </tr>
                        <tr>
                            <td>Deliverability Control</td>
                            <td class="check">Full control</td>
                            <td class="cross">Limited</td>
                        </tr>
                        <tr>
                            <td>Cost</td>
                            <td class="check">$10-20/month VPS</td>
                            <td class="cross">Free but labor-intensive</td>
                        </tr>
                        <tr>
                            <td>Scalability</td>
                            <td class="check">Unlimited</td>
                            <td class="cross">Limited by accounts</td>
                        </tr>
                    </tbody>
                </table>
                <p><small>*Limited only by your VPS capacity and ISP policies</small></p>
            </div>
            
            <div class="section">
                <h2><span class="section-icon">🚀</span>Quick Setup Guide</h2>
                <div class="steps">
                    <div class="step">
                        <h3>Install Chrome Extension</h3>
                        <p>Load the extension in Chrome Developer mode and enable it for Gmail.</p>
                        <div class="code-block">
                            1. Go to chrome://extensions/<br>
                            2. Enable "Developer mode"<br>
                            3. Click "Load unpacked"<br>
                            4. Select the extension folder
                        </div>
                    </div>
                    
                    <div class="step">
                        <h3>Set Up VPS Email Server</h3>
                        <p>Install the email server on your VPS for unlimited sending capacity.</p>
                        <div class="code-block">
                            chmod +x install_vps_email.sh<br>
                            sudo ./install_vps_email.sh
                        </div>
                    </div>
                    
                    <div class="step">
                        <h3>Configure DNS Records</h3>
                        <p>Add the required DNS records to your domain for proper email authentication.</p>
                        <div class="code-block">
                            mail.yourdomain.com    A     YOUR_VPS_IP<br>
                            yourdomain.com         MX    10 mail.yourdomain.com<br>
                            yourdomain.com         TXT   "v=spf1 ip4:YOUR_VPS_IP ~all"
                        </div>
                    </div>
                    
                    <div class="step">
                        <h3>Start Sending</h3>
                        <p>Access the web dashboard and start your first email campaign.</p>
                        <div class="code-block">
                            Dashboard: http://YOUR_VPS_IP:5000<br>
                            Upload CSV → Configure campaign → Send!
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="highlight">
                <strong>🎉 Ready to Send 10,000+ Emails Per Day?</strong><br>
                This complete solution gives you everything you need for professional email automation without the limitations of Gmail accounts.
            </div>
            
            <div class="section">
                <h2><span class="section-icon">📁</span>What's Included</h2>
                <ul style="line-height: 1.8; color: #5f6368;">
                    <li><strong>Chrome Extension:</strong> Authentic MailSuite UI integrated into Gmail</li>
                    <li><strong>VPS Email Server:</strong> Python-based server for unlimited sending</li>
                    <li><strong>Web Dashboard:</strong> Real-time campaign monitoring and management</li>
                    <li><strong>Installation Scripts:</strong> One-click VPS setup with all dependencies</li>
                    <li><strong>DNS Configuration:</strong> Complete guide for proper email authentication</li>
                    <li><strong>Sample Data:</strong> Test CSV files and email templates</li>
                    <li><strong>Documentation:</strong> Step-by-step setup and usage instructions</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin: 40px 0;">
                <button class="button" onclick="testExtension()">🧪 Test Extension</button>
                <button class="button secondary" onclick="downloadSampleCSV()">📥 Download Sample CSV</button>
                <button class="button secondary" onclick="viewVPSSetup()">🖥️ VPS Setup Guide</button>
            </div>
        </div>
    </div>
    
    <script>
        function testExtension() {
            alert('🚀 Extension Test:\n\n1. Load the extension in Chrome\n2. Go to Gmail\n3. Look for "MailSuite" in the sidebar\n4. Click "Compose" and look for the MailSuite button\n5. Upload a CSV file and start your first campaign!');
        }
        
        function downloadSampleCSV() {
            const csvContent = `firstName,lastName,email,company
John,Doe,<EMAIL>,Acme Corp
Jane,Smith,<EMAIL>,Tech Inc
Bob,Johnson,<EMAIL>,Innovation LLC
Alice,Brown,<EMAIL>,Global Solutions
Mike,Wilson,<EMAIL>,Big Company
Sarah,Davis,<EMAIL>,Creative Agency
Tom,Miller,<EMAIL>,Business Consulting
Lisa,Garcia,<EMAIL>,Digital Marketing
David,Rodriguez,<EMAIL>,Software Solutions
Emma,Martinez,<EMAIL>,Design Studio`;
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'mailsuite_sample_contacts.csv';
            a.click();
            
            URL.revokeObjectURL(url);
            
            alert('📥 Sample CSV downloaded!\n\nUse this file to test the mail merge functionality. It contains 10 sample contacts with firstName, lastName, email, and company fields.');
        }
        
        function viewVPSSetup() {
            alert('🖥️ VPS Setup:\n\n1. Get a VPS (DigitalOcean, Linode, AWS, etc.)\n2. Run the installation script\n3. Configure DNS records\n4. Access dashboard at http://YOUR_VPS_IP:5000\n5. Start sending unlimited emails!\n\nSee vps-email-setup.md for detailed instructions.');
        }
        
        // Auto-scroll animation
        window.addEventListener('load', () => {
            const sections = document.querySelectorAll('.section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });
            
            sections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'all 0.6s ease';
                observer.observe(section);
            });
        });
    </script>
</body>
</html>
