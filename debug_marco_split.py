#!/usr/bin/env python3
"""
Debug version for IT_DEPOSITORS_MARCO file
Shows detailed information about what's happening
"""

import pandas as pd
import os
import math
from pathlib import Path
import glob
import traceback

def main():
    print("🔍 DEBUG: IT Depositors Marco File Analysis")
    print("=" * 60)
    
    # Step 1: Check current directory and files
    print(f"📁 Current directory: {os.getcwd()}")
    print(f"\n📋 ALL files in current directory:")
    
    all_files = os.listdir(".")
    for file in sorted(all_files):
        if os.path.isfile(file):
            size = os.path.getsize(file) / 1024 / 1024  # MB
            print(f"   📄 {file} ({size:.2f} MB)")
        else:
            print(f"   📁 {file}/")
    
    # Step 2: Look specifically for Excel files
    print(f"\n📊 Excel files found:")
    excel_files = glob.glob("*.xlsx") + glob.glob("*.xls") + glob.glob("*.xlsm")
    if excel_files:
        for file in excel_files:
            size = os.path.getsize(file) / 1024 / 1024
            print(f"   📊 {file} ({size:.2f} MB)")
    else:
        print("   ❌ No Excel files found!")
    
    # Step 3: Look for files containing "marco" or "depositor"
    print(f"\n🔍 Files containing 'marco' or 'depositor':")
    marco_files = []
    for file in all_files:
        if os.path.isfile(file):
            if 'marco' in file.lower() or 'depositor' in file.lower():
                marco_files.append(file)
                size = os.path.getsize(file) / 1024 / 1024
                print(f"   ✅ {file} ({size:.2f} MB)")
    
    if not marco_files:
        print("   ❌ No files containing 'marco' or 'depositor' found!")
    
    # Step 4: Try to find the specific file
    possible_names = [
        "IT_DEPOSITORS_MARCO 9.9k.xlsx",
        "IT_DEPOSITORS_MARCO 9.9k.xls",
        "IT_DEPOSITORS_MARCO_9.9k.xlsx",
        "IT DEPOSITORS MARCO 9.9k.xlsx",
        "IT_DEPOSITORS_MARCO 9.9k.xlsm"
    ]
    
    print(f"\n🎯 Looking for specific file names:")
    found_file = None
    for name in possible_names:
        exists = os.path.exists(name)
        print(f"   {'✅' if exists else '❌'} {name}")
        if exists and not found_file:
            found_file = name
    
    # Step 5: If no exact match, ask user to select
    if not found_file and excel_files:
        print(f"\n❓ No exact match found. Please select your file:")
        for i, file in enumerate(excel_files, 1):
            print(f"   {i}. {file}")
        
        while True:
            try:
                choice = input(f"Select file number (1-{len(excel_files)}): ")
                index = int(choice) - 1
                if 0 <= index < len(excel_files):
                    found_file = excel_files[index]
                    break
                else:
                    print("❌ Invalid choice!")
            except ValueError:
                print("❌ Please enter a number!")
    
    if not found_file:
        print(f"\n❌ No Excel file found to process!")
        input("Press Enter to exit...")
        return
    
    print(f"\n✅ Processing file: {found_file}")
    
    try:
        # Step 6: Try to read the Excel file
        print(f"\n📖 Attempting to read Excel file...")
        df = pd.read_excel(found_file)
        
        print(f"✅ File read successfully!")
        print(f"📊 Shape: {df.shape} (rows: {len(df)}, columns: {len(df.columns)})")
        
        # Step 7: Show detailed column information
        print(f"\n📋 Column details:")
        for i, col in enumerate(df.columns, 1):
            non_null_count = df[col].count()
            null_count = len(df) - non_null_count
            print(f"   {i:2d}. '{col}' - {non_null_count} values, {null_count} nulls")
        
        # Step 8: Show sample data
        print(f"\n📖 First 5 rows:")
        print(df.head().to_string())
        
        # Step 9: Auto-detect columns with detailed analysis
        print(f"\n🔍 Column analysis for Name/Email/Phone detection:")
        
        name_candidates = []
        email_candidates = []
        phone_candidates = []
        
        for i, col in enumerate(df.columns):
            col_lower = col.lower().strip()
            sample_values = df[col].dropna().head(3).tolist()
            
            print(f"\n   Column {i+1}: '{col}'")
            print(f"      Sample values: {sample_values}")
            
            # Check for name patterns
            if any(word in col_lower for word in ['name', 'nome', 'first']):
                name_candidates.append((i+1, col))
                print(f"      → Potential NAME column")
            
            # Check for email patterns
            if any(word in col_lower for word in ['email', 'mail']):
                email_candidates.append((i+1, col))
                print(f"      → Potential EMAIL column")
            
            # Check for phone patterns
            if any(word in col_lower for word in ['phone', 'tel', 'mobile', 'cell']):
                phone_candidates.append((i+1, col))
                print(f"      → Potential PHONE column")
        
        print(f"\n🎯 Detection results:")
        print(f"   Name candidates: {name_candidates}")
        print(f"   Email candidates: {email_candidates}")
        print(f"   Phone candidates: {phone_candidates}")
        
        # Step 10: Manual selection
        print(f"\n❓ Please manually select columns:")
        
        # Select Name column
        print(f"\nWhich column contains NAMES?")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i}. {col}")
        
        while True:
            try:
                choice = input("Select Name column number: ")
                name_col_idx = int(choice) - 1
                if 0 <= name_col_idx < len(df.columns):
                    name_col = df.columns[name_col_idx]
                    break
                else:
                    print("❌ Invalid choice!")
            except ValueError:
                print("❌ Please enter a number!")
        
        # Select Email column
        print(f"\nWhich column contains EMAILS?")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i}. {col}")
        
        while True:
            try:
                choice = input("Select Email column number: ")
                email_col_idx = int(choice) - 1
                if 0 <= email_col_idx < len(df.columns):
                    email_col = df.columns[email_col_idx]
                    break
                else:
                    print("❌ Invalid choice!")
            except ValueError:
                print("❌ Please enter a number!")
        
        # Select Phone column
        print(f"\nWhich column contains PHONE numbers?")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i}. {col}")
        print(f"   0. Skip phone column")
        
        while True:
            try:
                choice = input("Select Phone column number (or 0 to skip): ")
                if choice == "0":
                    phone_col = None
                    break
                phone_col_idx = int(choice) - 1
                if 0 <= phone_col_idx < len(df.columns):
                    phone_col = df.columns[phone_col_idx]
                    break
                else:
                    print("❌ Invalid choice!")
            except ValueError:
                print("❌ Please enter a number!")
        
        print(f"\n✅ Selected columns:")
        print(f"   Name: {name_col}")
        print(f"   Email: {email_col}")
        print(f"   Phone: {phone_col if phone_col else 'None'}")
        
        # Step 11: Process the data
        print(f"\n🧹 Processing data...")
        
        if phone_col:
            df_filtered = df[[name_col, email_col, phone_col]].copy()
            df_filtered.columns = ['Name', 'Email', 'Phone']
        else:
            df_filtered = df[[name_col, email_col]].copy()
            df_filtered.columns = ['Name', 'Email']
            df_filtered['Phone'] = ''
        
        print(f"📊 After column selection: {len(df_filtered)} rows")
        
        # Clean data step by step
        print(f"\n🧹 Cleaning data step by step:")
        
        # Remove rows without email
        before_email_clean = len(df_filtered)
        df_filtered = df_filtered.dropna(subset=['Email'])
        after_email_clean = len(df_filtered)
        print(f"   After removing empty emails: {after_email_clean} rows ({before_email_clean - after_email_clean} removed)")
        
        # Remove invalid emails
        before_at_clean = len(df_filtered)
        df_filtered = df_filtered[df_filtered['Email'].str.contains('@', na=False)]
        after_at_clean = len(df_filtered)
        print(f"   After removing emails without @: {after_at_clean} rows ({before_at_clean - after_at_clean} removed)")
        
        # Fill empty names and phones
        df_filtered['Name'] = df_filtered['Name'].fillna('')
        df_filtered['Phone'] = df_filtered['Phone'].fillna('')
        
        final_rows = len(df_filtered)
        print(f"   Final valid rows: {final_rows}")
        
        if final_rows == 0:
            print(f"\n❌ No valid data remaining after cleaning!")
            print(f"💡 Check if your email column actually contains valid email addresses")
            input("Press Enter to exit...")
            return
        
        # Show sample of final data
        print(f"\n📋 Sample of final cleaned data:")
        print(df_filtered.head(3).to_string(index=False))
        
        # Step 12: Split into files
        rows_per_file = 500
        total_files = math.ceil(final_rows / rows_per_file)
        
        output_folder = "it_depositors_marco_csv_debug"
        Path(output_folder).mkdir(exist_ok=True)
        
        print(f"\n📁 Creating {total_files} files with {rows_per_file} rows each...")
        print(f"📁 Output folder: {output_folder}")
        
        for i in range(total_files):
            start_row = i * rows_per_file
            end_row = min((i + 1) * rows_per_file, final_rows)
            
            chunk = df_filtered.iloc[start_row:end_row]
            filename = f"marco_part_{i+1:03d}_of_{total_files:03d}.csv"
            filepath = os.path.join(output_folder, filename)
            
            chunk.to_csv(filepath, index=False)
            print(f"✅ Created {filename} ({len(chunk)} rows)")
        
        print(f"\n🎉 SUCCESS! Created {total_files} CSV files in '{output_folder}' folder!")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        print(f"\n🔍 Full error details:")
        traceback.print_exc()
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
