#!/bin/bash

# MailSuite VPS Quick Setup Script
# Run this on your VPS to set up email sending infrastructure

echo "🚀 MailSuite VPS Email Server Setup"
echo "=================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ Please run this script as root (use sudo)${NC}"
    exit 1
fi

echo -e "${BLUE}📋 This script will install:${NC}"
echo "   • Postfix (SMTP server)"
echo "   • Python email server"
echo "   • Web dashboard"
echo "   • DKIM authentication"
echo "   • Firewall configuration"
echo ""

read -p "Continue? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
fi

# Get domain and IP
echo ""
echo -e "${YELLOW}📝 Configuration${NC}"
read -p "Enter your domain name (e.g., yourdomain.com): " DOMAIN
read -p "Enter your VPS IP address: " VPS_IP

if [ -z "$DOMAIN" ] || [ -z "$VPS_IP" ]; then
    echo -e "${RED}❌ Domain and IP are required!${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ Configuration:${NC}"
echo "   Domain: $DOMAIN"
echo "   VPS IP: $VPS_IP"
echo "   Mail server: mail.$DOMAIN"
echo ""

# Update system
echo -e "${BLUE}📦 Updating system packages...${NC}"
apt update && apt upgrade -y

# Install required packages
echo -e "${BLUE}📦 Installing required packages...${NC}"
apt install -y python3 python3-pip postfix mailutils ufw fail2ban opendkim opendkim-tools

# Install Python packages
echo -e "${BLUE}🐍 Installing Python packages...${NC}"
pip3 install flask sqlite3

# Configure firewall
echo -e "${BLUE}🔥 Configuring firewall...${NC}"
ufw allow 22    # SSH
ufw allow 25    # SMTP
ufw allow 587   # SMTP submission
ufw allow 5000  # Web dashboard
ufw --force enable

# Configure Postfix
echo -e "${BLUE}📧 Configuring Postfix...${NC}"
cp /etc/postfix/main.cf /etc/postfix/main.cf.backup

cat > /etc/postfix/main.cf << EOF
# Basic configuration
myhostname = mail.$DOMAIN
mydomain = $DOMAIN
myorigin = \$mydomain
inet_interfaces = all
inet_protocols = ipv4
mydestination = \$myhostname, localhost.\$mydomain, localhost, \$mydomain

# Network configuration
mynetworks = *********/8 [::ffff:*********]/104 [::1]/128 $VPS_IP/32
relayhost = 

# Mail delivery
home_mailbox = Maildir/
mailbox_command = 

# Security
smtpd_banner = \$myhostname ESMTP
biff = no
append_dot_mydomain = no
readme_directory = no

# TLS configuration
smtpd_use_tls = yes
smtpd_tls_cert_file = /etc/ssl/certs/ssl-cert-snakeoil.pem
smtpd_tls_key_file = /etc/ssl/private/ssl-cert-snakeoil.key
smtpd_tls_security_level = may
smtp_tls_security_level = may

# SMTP restrictions
smtpd_relay_restrictions = permit_mynetworks permit_sasl_authenticated defer_unauth_destination
smtpd_recipient_restrictions = permit_mynetworks permit_sasl_authenticated reject_unauth_destination

# Message size limit (50MB)
message_size_limit = 52428800
mailbox_size_limit = 0
recipient_delimiter = +

# DKIM configuration
milter_protocol = 2
milter_default_action = accept
smtpd_milters = inet:localhost:12301
non_smtpd_milters = inet:localhost:12301
EOF

# Configure DKIM
echo -e "${BLUE}🔐 Configuring DKIM...${NC}"
mkdir -p /etc/opendkim/keys/$DOMAIN
cd /etc/opendkim/keys/$DOMAIN
opendkim-genkey -t -s default -d $DOMAIN

chown opendkim:opendkim /etc/opendkim/keys/$DOMAIN/default.private
chmod 600 /etc/opendkim/keys/$DOMAIN/default.private

cat > /etc/opendkim.conf << EOF
AutoRestart             Yes
AutoRestartRate         10/1h
UMask                   002
Syslog                  yes
SyslogSuccess           Yes
LogWhy                  Yes
Canonicalization        relaxed/simple
ExternalIgnoreList      refile:/etc/opendkim/TrustedHosts
InternalHosts           refile:/etc/opendkim/TrustedHosts
KeyTable                refile:/etc/opendkim/KeyTable
SigningTable            refile:/etc/opendkim/SigningTable
Mode                    sv
PidFile                 /var/run/opendkim/opendkim.pid
SignatureAlgorithm      rsa-sha256
UserID                  opendkim:opendkim
Socket                  inet:12301@localhost
EOF

echo "127.0.0.1" > /etc/opendkim/TrustedHosts
echo "localhost" >> /etc/opendkim/TrustedHosts
echo "$DOMAIN" >> /etc/opendkim/TrustedHosts
echo "mail.$DOMAIN" >> /etc/opendkim/TrustedHosts

echo "default._domainkey.$DOMAIN $DOMAIN:default:/etc/opendkim/keys/$DOMAIN/default.private" > /etc/opendkim/KeyTable
echo "*@$DOMAIN default._domainkey.$DOMAIN" > /etc/opendkim/SigningTable

# Create email server directory
echo -e "${BLUE}📁 Setting up email server...${NC}"
mkdir -p /opt/email-server
cd /opt/email-server

# Create Python email server
cat > /opt/email-server/email_server.py << 'EOF'
#!/usr/bin/env python3
"""
MailSuite VPS Email Server
High-volume email sending with web dashboard
"""

from flask import Flask, request, jsonify, render_template_string
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import time
import threading
import queue
import logging
import sqlite3
from datetime import datetime
import uuid

app = Flask(__name__)

# Configuration
SMTP_SERVER = "localhost"
SMTP_PORT = 25
DEFAULT_FROM_EMAIL = f"noreply@{DOMAIN}"
MAX_EMAILS_PER_HOUR = 1000
DELAY_BETWEEN_EMAILS = 3

# Email queue and statistics
email_queue = queue.Queue()
sent_count = 0
failed_count = 0
is_running = False

# Simple web dashboard
DASHBOARD_HTML = '''
<!DOCTYPE html>
<html>
<head>
    <title>MailSuite Email Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { text-align: center; margin-bottom: 30px; }
        .stats { display: flex; justify-content: space-around; margin: 20px 0; }
        .stat { text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; }
        .stat h3 { margin: 0; color: #4285f4; font-size: 24px; }
        .stat p { margin: 5px 0 0 0; color: #666; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .button { padding: 10px 20px; background: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .button:hover { background: #3367d6; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 MailSuite Email Dashboard</h1>
            <p>VPS Email Server Control Panel</p>
        </div>
        
        <div class="stats">
            <div class="stat">
                <h3 id="queue-size">0</h3>
                <p>Queue Size</p>
            </div>
            <div class="stat">
                <h3 id="sent-count">0</h3>
                <p>Sent Today</p>
            </div>
            <div class="stat">
                <h3 id="failed-count">0</h3>
                <p>Failed</p>
            </div>
            <div class="stat">
                <h3 id="status">Stopped</h3>
                <p>Status</p>
            </div>
        </div>
        
        <div class="section">
            <h3>Server Control</h3>
            <button class="button" onclick="startServer()">Start Sender</button>
            <button class="button" onclick="stopServer()">Stop Sender</button>
            <button class="button" onclick="updateStatus()">Refresh Status</button>
        </div>
        
        <div class="section">
            <h3>Send Test Email</h3>
            <div class="form-group">
                <label>To Email:</label>
                <input type="email" id="test-email" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label>Subject:</label>
                <input type="text" id="test-subject" placeholder="Test Subject">
            </div>
            <div class="form-group">
                <label>Message:</label>
                <textarea id="test-message" rows="4" placeholder="Test message content"></textarea>
            </div>
            <button class="button" onclick="sendTestEmail()">Send Test Email</button>
        </div>
        
        <div id="message-area"></div>
    </div>
    
    <script>
        function showMessage(message, type = 'success') {
            document.getElementById('message-area').innerHTML = 
                `<div class="status ${type}">${message}</div>`;
        }
        
        function updateStatus() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('queue-size').textContent = data.queue_size;
                    document.getElementById('sent-count').textContent = data.sent_count;
                    document.getElementById('failed-count').textContent = data.failed_count;
                    document.getElementById('status').textContent = data.is_running ? 'Running' : 'Stopped';
                });
        }
        
        function startServer() {
            fetch('/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    showMessage('Email sender started!');
                    updateStatus();
                });
        }
        
        function stopServer() {
            fetch('/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    showMessage('Email sender stopped!');
                    updateStatus();
                });
        }
        
        function sendTestEmail() {
            const emailData = {
                to_email: document.getElementById('test-email').value,
                subject: document.getElementById('test-subject').value,
                body: document.getElementById('test-message').value
            };
            
            fetch('/send-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(emailData)
            })
            .then(response => response.json())
            .then(data => {
                showMessage('Test email queued successfully!');
                updateStatus();
            })
            .catch(error => {
                showMessage('Error: ' + error.message, 'error');
            });
        }
        
        // Auto-refresh every 5 seconds
        setInterval(updateStatus, 5000);
        updateStatus();
    </script>
</body>
</html>
'''

class EmailSender:
    def __init__(self):
        self.is_running = False
        
    def send_email(self, to_email, subject, body, from_email=None):
        global sent_count, failed_count
        
        if not from_email:
            from_email = DEFAULT_FROM_EMAIL
            
        try:
            msg = MIMEMultipart()
            msg['From'] = from_email
            msg['To'] = to_email
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'html'))
            
            server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
            server.send_message(msg)
            server.quit()
            
            sent_count += 1
            logging.info(f"Email sent to {to_email}")
            return True
            
        except Exception as e:
            failed_count += 1
            logging.error(f"Failed to send email to {to_email}: {e}")
            return False
    
    def process_queue(self):
        while self.is_running:
            try:
                email_data = email_queue.get(timeout=1)
                self.send_email(**email_data)
                time.sleep(DELAY_BETWEEN_EMAILS)
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Queue processing error: {e}")

sender = EmailSender()

@app.route('/')
def dashboard():
    return render_template_string(DASHBOARD_HTML)

@app.route('/send-email', methods=['POST'])
def send_email():
    data = request.json
    
    required_fields = ['to_email', 'subject', 'body']
    if not all(field in data for field in required_fields):
        return jsonify({'error': 'Missing required fields'}), 400
    
    email_queue.put({
        'to_email': data['to_email'],
        'subject': data['subject'],
        'body': data['body'],
        'from_email': data.get('from_email', DEFAULT_FROM_EMAIL)
    })
    
    return jsonify({'status': 'queued', 'queue_size': email_queue.qsize()})

@app.route('/status', methods=['GET'])
def status():
    global sent_count, failed_count
    return jsonify({
        'queue_size': email_queue.qsize(),
        'is_running': sender.is_running,
        'sent_count': sent_count,
        'failed_count': failed_count
    })

@app.route('/start', methods=['POST'])
def start_sender():
    if not sender.is_running:
        sender.is_running = True
        thread = threading.Thread(target=sender.process_queue)
        thread.daemon = True
        thread.start()
        return jsonify({'status': 'started'})
    return jsonify({'status': 'already_running'})

@app.route('/stop', methods=['POST'])
def stop_sender():
    sender.is_running = False
    return jsonify({'status': 'stopped'})

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    print(f"Starting MailSuite Email Server...")
    print(f"Dashboard: http://{VPS_IP}:5000")
    app.run(host='0.0.0.0', port=5000, debug=False)
EOF

# Replace DOMAIN placeholder in Python script
sed -i "s/DOMAIN = .*/DOMAIN = \"$DOMAIN\"/" /opt/email-server/email_server.py

# Create systemd service
cat > /etc/systemd/system/email-server.service << EOF
[Unit]
Description=MailSuite Email Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/email-server
ExecStart=/usr/bin/python3 /opt/email-server/email_server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Start services
echo -e "${BLUE}🚀 Starting services...${NC}"
systemctl enable opendkim
systemctl start opendkim
systemctl restart postfix
systemctl enable postfix

systemctl daemon-reload
systemctl enable email-server
systemctl start email-server

# Create DNS setup guide
cat > /opt/email-server/dns_setup.txt << EOF
=== DNS CONFIGURATION REQUIRED ===

Add these DNS records to your domain ($DOMAIN):

1. A Record:
   mail.$DOMAIN    A    $VPS_IP

2. MX Record:
   $DOMAIN         MX   10 mail.$DOMAIN

3. SPF Record:
   $DOMAIN         TXT  "v=spf1 ip4:$VPS_IP ~all"

4. DMARC Record:
   _dmarc.$DOMAIN  TXT  "v=DMARC1; p=quarantine; rua=mailto:dmarc@$DOMAIN"

5. DKIM Record:
   default._domainkey.$DOMAIN  TXT  "$(cat /etc/opendkim/keys/$DOMAIN/default.txt | grep -v '^default._domainkey' | tr -d '\n\t " ')"

6. PTR Record (Contact your VPS provider):
   $VPS_IP         PTR  mail.$DOMAIN

=== TESTING ===
1. Web dashboard: http://$VPS_IP:5000
2. Send test email from dashboard
3. Check logs: tail -f /var/log/mail.log
EOF

# Final output
echo ""
echo -e "${GREEN}🎉 INSTALLATION COMPLETE!${NC}"
echo ""
echo -e "${YELLOW}📋 Next Steps:${NC}"
echo "1. Configure DNS records (see /opt/email-server/dns_setup.txt)"
echo "2. Set up PTR record with your VPS provider"
echo "3. Access dashboard: http://$VPS_IP:5000"
echo "4. Send test email from dashboard"
echo ""
echo -e "${YELLOW}📁 Important Files:${NC}"
echo "• DNS setup: /opt/email-server/dns_setup.txt"
echo "• Email logs: /var/log/mail.log"
echo "• Server logs: journalctl -u email-server"
echo ""
echo -e "${YELLOW}🔐 DKIM Public Key:${NC}"
echo "Add this to your DNS as TXT record for default._domainkey.$DOMAIN:"
echo ""
cat /etc/opendkim/keys/$DOMAIN/default.txt
echo ""
echo -e "${GREEN}Happy emailing! 🚀${NC}"
