#!/usr/bin/env python3
"""
File Finder - Locate your Excel file and show exact path
"""

import os
import glob

def find_excel_files():
    print("🔍 Excel File Finder")
    print("=" * 40)
    
    # Show current directory
    current_dir = os.getcwd()
    print(f"📁 Current directory: {current_dir}")
    
    # List ALL files in current directory
    print(f"\n📋 ALL files in current directory:")
    try:
        files = os.listdir(".")
        if not files:
            print("   (No files found)")
        else:
            for file in sorted(files):
                if os.path.isfile(file):
                    size = os.path.getsize(file)
                    print(f"   📄 {file} ({size:,} bytes)")
                else:
                    print(f"   📁 {file}/")
    except Exception as e:
        print(f"   ❌ Error listing files: {e}")
    
    # Look for Excel files specifically
    print(f"\n📊 Looking for Excel files...")
    excel_patterns = ['*.xlsx', '*.xls', '*.xlsm']
    excel_files = []
    
    for pattern in excel_patterns:
        found = glob.glob(pattern)
        excel_files.extend(found)
    
    if excel_files:
        print(f"✅ Found {len(excel_files)} Excel file(s):")
        for file in excel_files:
            full_path = os.path.abspath(file)
            size = os.path.getsize(file)
            print(f"   📊 {file}")
            print(f"      Full path: {full_path}")
            print(f"      Size: {size:,} bytes")
    else:
        print("❌ No Excel files found in current directory")
    
    # Look for files with "December" or "Fresh" in name
    print(f"\n🔍 Looking for files with 'December' or 'Fresh' in name...")
    matching_files = []
    try:
        for file in os.listdir("."):
            if os.path.isfile(file):
                if 'december' in file.lower() or 'fresh' in file.lower():
                    matching_files.append(file)
    except Exception as e:
        print(f"❌ Error searching files: {e}")
    
    if matching_files:
        print(f"✅ Found {len(matching_files)} matching file(s):")
        for file in matching_files:
            full_path = os.path.abspath(file)
            size = os.path.getsize(file)
            print(f"   📄 {file}")
            print(f"      Full path: {full_path}")
            print(f"      Size: {size:,} bytes")
    else:
        print("❌ No files with 'December' or 'Fresh' found")
    
    # Search in common folders
    print(f"\n🔍 Searching common folders...")
    common_folders = [
        os.path.expanduser("~/Desktop"),
        os.path.expanduser("~/Downloads"),
        os.path.expanduser("~/Documents"),
        "C:\\Users\\<USER>\\Desktop",
        "C:\\Users\\<USER>\\Downloads",
        "C:\\Users\\<USER>\\Documents"
    ]
    
    for folder in common_folders:
        if os.path.exists(folder):
            print(f"\n📁 Checking: {folder}")
            try:
                files = os.listdir(folder)
                excel_files_in_folder = [f for f in files if f.endswith(('.xlsx', '.xls', '.xlsm'))]
                if excel_files_in_folder:
                    print(f"   ✅ Found {len(excel_files_in_folder)} Excel file(s):")
                    for file in excel_files_in_folder[:5]:  # Show first 5
                        print(f"      📊 {file}")
                else:
                    print(f"   ❌ No Excel files found")
            except Exception as e:
                print(f"   ❌ Cannot access: {e}")
    
    return excel_files

def create_working_script(excel_files):
    """Create a script that works with the found files"""
    if not excel_files:
        print(f"\n💡 SOLUTION: Put your Excel file in this folder:")
        print(f"   {os.getcwd()}")
        print(f"\n📋 Steps:")
        print(f"   1. Find your 'Fresh December9.5k.xlsx' file")
        print(f"   2. Copy it to: {os.getcwd()}")
        print(f"   3. Run this script again")
        return
    
    # Create a script that uses the found file
    excel_file = excel_files[0]
    script_content = f'''#!/usr/bin/env python3
"""
Auto-generated Excel splitter for your specific file
"""

import pandas as pd
import os
from pathlib import Path

def main():
    print("🚀 Excel Splitter - Auto-configured")
    print("=" * 40)
    
    excel_file = "{excel_file}"
    print(f"📊 Processing: {{excel_file}}")
    
    try:
        # Read Excel file
        df = pd.read_excel(excel_file)
        print(f"✅ Loaded: {{len(df)}} rows, {{len(df.columns)}} columns")
        
        # Create output folder
        output_folder = "csv_email_lists"
        Path(output_folder).mkdir(exist_ok=True)
        
        # Split into 200-row files
        rows_per_file = 200
        total_files = (len(df) + rows_per_file - 1) // rows_per_file
        
        print(f"📁 Creating {{total_files}} CSV files...")
        
        for i in range(total_files):
            start = i * rows_per_file
            end = min((i + 1) * rows_per_file, len(df))
            
            chunk = df.iloc[start:end]
            filename = f"Fresh_December_part_{{i+1:03d}}.csv"
            filepath = os.path.join(output_folder, filename)
            
            chunk.to_csv(filepath, index=False)
            print(f"✅ {{filename}} ({{len(chunk)}} rows)")
        
        print(f"\\n🎉 Success! {{total_files}} CSV files created in '{{output_folder}}' folder")
        
    except Exception as e:
        print(f"❌ Error: {{e}}")
    
    input("\\nPress Enter to exit...")

if __name__ == "__main__":
    main()
'''
    
    with open("auto_split.py", "w") as f:
        f.write(script_content)
    
    print(f"\n✅ Created 'auto_split.py' configured for your file!")
    print(f"📋 To use it:")
    print(f"   1. Run: python auto_split.py")
    print(f"   2. Your CSV files will be created automatically")

def main():
    excel_files = find_excel_files()
    create_working_script(excel_files)
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
'''
