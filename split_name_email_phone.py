#!/usr/bin/env python3
"""
Excel to CSV Splitter - Name, Email, Phone Only
Keeps only Name, Email, Phone columns and splits into 200-row files
"""

import pandas as pd
import os
import math
from pathlib import Path

def find_excel_file():
    """Find Excel file in current directory"""
    excel_files = []
    for file in os.listdir("."):
        if file.endswith(('.xlsx', '.xls', '.xlsm')):
            excel_files.append(file)
    
    if not excel_files:
        print("❌ No Excel files found!")
        return None
    
    if len(excel_files) == 1:
        return excel_files[0]
    
    print("📋 Multiple Excel files found:")
    for i, file in enumerate(excel_files, 1):
        print(f"   {i}. {file}")
    
    while True:
        try:
            choice = input(f"Select file (1-{len(excel_files)}): ")
            index = int(choice) - 1
            if 0 <= index < len(excel_files):
                return excel_files[index]
            else:
                print("❌ Invalid choice!")
        except ValueError:
            print("❌ Please enter a number!")

def find_columns(df):
    """Find Name, Email, Phone columns (case insensitive)"""
    columns = df.columns.tolist()
    print(f"\n📋 Available columns: {columns}")
    
    # Find columns automatically
    name_col = None
    email_col = None
    phone_col = None
    
    for col in columns:
        col_lower = col.lower()
        
        # Find Name column
        if not name_col and ('name' in col_lower or 'nome' in col_lower):
            name_col = col
        
        # Find Email column
        if not email_col and ('email' in col_lower or 'mail' in col_lower or 'e-mail' in col_lower):
            email_col = col
        
        # Find Phone column
        if not phone_col and ('phone' in col_lower or 'tel' in col_lower or 'mobile' in col_lower or 'cell' in col_lower):
            phone_col = col
    
    print(f"\n🔍 Auto-detected columns:")
    print(f"   Name: {name_col if name_col else 'NOT FOUND'}")
    print(f"   Email: {email_col if email_col else 'NOT FOUND'}")
    print(f"   Phone: {phone_col if phone_col else 'NOT FOUND'}")
    
    # If not found automatically, ask user
    if not name_col:
        print(f"\n❓ Which column contains NAMES?")
        for i, col in enumerate(columns, 1):
            print(f"   {i}. {col}")
        while True:
            try:
                choice = input("Select Name column number: ")
                index = int(choice) - 1
                if 0 <= index < len(columns):
                    name_col = columns[index]
                    break
                else:
                    print("❌ Invalid choice!")
            except ValueError:
                print("❌ Please enter a number!")
    
    if not email_col:
        print(f"\n❓ Which column contains EMAILS?")
        for i, col in enumerate(columns, 1):
            print(f"   {i}. {col}")
        while True:
            try:
                choice = input("Select Email column number: ")
                index = int(choice) - 1
                if 0 <= index < len(columns):
                    email_col = columns[index]
                    break
                else:
                    print("❌ Invalid choice!")
            except ValueError:
                print("❌ Please enter a number!")
    
    if not phone_col:
        print(f"\n❓ Which column contains PHONE numbers?")
        for i, col in enumerate(columns, 1):
            print(f"   {i}. {col}")
        while True:
            try:
                choice = input("Select Phone column number: ")
                index = int(choice) - 1
                if 0 <= index < len(columns):
                    phone_col = columns[index]
                    break
                else:
                    print("❌ Invalid choice!")
            except ValueError:
                print("❌ Please enter a number!")
    
    return name_col, email_col, phone_col

def clean_and_filter_data(df, name_col, email_col, phone_col):
    """Clean data and keep only Name, Email, Phone columns"""
    print(f"\n🧹 Cleaning and filtering data...")
    
    original_rows = len(df)
    print(f"📊 Original rows: {original_rows:,}")
    
    # Select only the required columns
    try:
        df_filtered = df[[name_col, email_col, phone_col]].copy()
    except KeyError as e:
        print(f"❌ Error: Column not found: {e}")
        return None
    
    # Rename columns to standard names
    df_filtered.columns = ['Name', 'Email', 'Phone']
    
    # Remove rows where Email is empty (Email is required)
    df_filtered = df_filtered.dropna(subset=['Email'])
    
    # Remove rows where Email doesn't contain @
    df_filtered = df_filtered[df_filtered['Email'].str.contains('@', na=False)]
    
    # Fill empty Name and Phone with empty string
    df_filtered['Name'] = df_filtered['Name'].fillna('')
    df_filtered['Phone'] = df_filtered['Phone'].fillna('')
    
    # Remove completely empty rows
    df_filtered = df_filtered.dropna(how='all')
    
    final_rows = len(df_filtered)
    removed_rows = original_rows - final_rows
    
    print(f"✅ Final rows: {final_rows:,}")
    print(f"🗑️ Removed rows: {removed_rows:,}")
    print(f"📊 Columns: Name, Email, Phone")
    
    # Show sample data
    if len(df_filtered) > 0:
        print(f"\n📋 Sample data:")
        print(df_filtered.head(3).to_string(index=False))
    
    return df_filtered

def split_to_csv(df, rows_per_file=200):
    """Split dataframe into CSV files"""
    print(f"\n📁 Creating CSV files with {rows_per_file} rows each...")
    
    total_files = math.ceil(len(df) / rows_per_file)
    
    # Create output folder
    output_folder = "csv_name_email_phone"
    Path(output_folder).mkdir(exist_ok=True)
    
    print(f"📁 Output folder: {output_folder}")
    print(f"📊 Creating {total_files} files...")
    
    for i in range(total_files):
        start_row = i * rows_per_file
        end_row = min((i + 1) * rows_per_file, len(df))
        
        chunk = df.iloc[start_row:end_row]
        
        filename = f"contacts_part_{i+1:03d}_of_{total_files:03d}.csv"
        filepath = os.path.join(output_folder, filename)
        
        # Save to CSV
        chunk.to_csv(filepath, index=False)
        
        print(f"✅ {filename} ({len(chunk)} rows)")
    
    # Create summary file
    summary_file = os.path.join(output_folder, "SUMMARY.txt")
    with open(summary_file, 'w') as f:
        f.write("Email Campaign Files - Name, Email, Phone Only\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Total contacts: {len(df):,}\n")
        f.write(f"Files created: {total_files}\n")
        f.write(f"Rows per file: {rows_per_file}\n")
        f.write(f"Columns: Name, Email, Phone\n\n")
        f.write("Files:\n")
        for i in range(total_files):
            filename = f"contacts_part_{i+1:03d}_of_{total_files:03d}.csv"
            f.write(f"  • {filename}\n")
    
    print(f"\n🎉 Successfully created {total_files} CSV files!")
    print(f"📁 Location: {output_folder}/")
    print(f"📄 Summary: {summary_file}")
    
    return True

def main():
    print("🚀 Excel to CSV Splitter - Name, Email, Phone Only")
    print("=" * 60)
    
    # Find Excel file
    excel_file = find_excel_file()
    if not excel_file:
        input("Press Enter to exit...")
        return
    
    print(f"📊 Processing: {excel_file}")
    
    try:
        # Read Excel file
        df = pd.read_excel(excel_file)
        print(f"✅ Loaded: {len(df):,} rows, {len(df.columns)} columns")
        
        # Find Name, Email, Phone columns
        name_col, email_col, phone_col = find_columns(df)
        
        # Clean and filter data
        df_clean = clean_and_filter_data(df, name_col, email_col, phone_col)
        if df_clean is None:
            input("Press Enter to exit...")
            return
        
        if len(df_clean) == 0:
            print("❌ No valid data found after cleaning!")
            input("Press Enter to exit...")
            return
        
        # Split into CSV files
        success = split_to_csv(df_clean, rows_per_file=200)
        
        if success:
            print(f"\n✅ All done! Your CSV files contain only Name, Email, Phone columns.")
            print(f"📧 Perfect for MailSuite email campaigns!")
            print(f"💡 Upload these files to MailSuite extension for bulk email sending.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
