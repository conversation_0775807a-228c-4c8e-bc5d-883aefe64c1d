#!/usr/bin/env python3
"""
VPS Email Server for High-Volume Email Sending
Supports 10K+ emails per day with proper throttling and queue management
"""

from flask import Flask, request, jsonify, render_template_string
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.base import MIMEBase
from email import encoders
import time
import threading
import queue
import logging
import json
import sqlite3
from datetime import datetime, timedelta
import uuid
import os
from werkzeug.utils import secure_filename

app = Flask(__name__)

# Configuration
SMTP_SERVER = "localhost"  # Use your VPS SMTP server
SMTP_PORT = 587
SMTP_USERNAME = ""  # If authentication required
SMTP_PASSWORD = ""  # If authentication required
DEFAULT_FROM_EMAIL = "<EMAIL>"
MAX_EMAILS_PER_HOUR = 1000  # Adjust based on your needs
DELAY_BETWEEN_EMAILS = 3  # seconds

# Email queue and statistics
email_queue = queue.Queue()
sent_count = 0
failed_count = 0
is_running = False

# Database setup
def init_db():
    conn = sqlite3.connect('email_campaigns.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS campaigns (
            id TEXT PRIMARY KEY,
            name TEXT,
            subject TEXT,
            body TEXT,
            total_emails INTEGER,
            sent_emails INTEGER,
            failed_emails INTEGER,
            status TEXT,
            created_at TIMESTAMP,
            started_at TIMESTAMP,
            completed_at TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS email_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            campaign_id TEXT,
            recipient_email TEXT,
            status TEXT,
            error_message TEXT,
            sent_at TIMESTAMP,
            FOREIGN KEY (campaign_id) REFERENCES campaigns (id)
        )
    ''')
    
    conn.commit()
    conn.close()

class EmailSender:
    def __init__(self):
        self.is_running = False
        self.current_campaign = None
        
    def send_email(self, to_email, subject, body, from_email=None, campaign_id=None):
        global sent_count, failed_count
        
        if not from_email:
            from_email = DEFAULT_FROM_EMAIL
            
        try:
            msg = MIMEMultipart('alternative')
            msg['From'] = from_email
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Add HTML body
            html_part = MIMEText(body, 'html')
            msg.attach(html_part)
            
            # Connect to SMTP server
            if SMTP_USERNAME and SMTP_PASSWORD:
                server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
                server.starttls()
                server.login(SMTP_USERNAME, SMTP_PASSWORD)
            else:
                server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
            
            # Send email
            server.send_message(msg)
            server.quit()
            
            # Log success
            self.log_email(campaign_id, to_email, 'sent', None)
            sent_count += 1
            
            logging.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            # Log failure
            self.log_email(campaign_id, to_email, 'failed', str(e))
            failed_count += 1
            
            logging.error(f"Failed to send email to {to_email}: {e}")
            return False
    
    def log_email(self, campaign_id, recipient_email, status, error_message):
        try:
            conn = sqlite3.connect('email_campaigns.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO email_logs (campaign_id, recipient_email, status, error_message, sent_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (campaign_id, recipient_email, status, error_message, datetime.now()))
            
            conn.commit()
            conn.close()
        except Exception as e:
            logging.error(f"Failed to log email: {e}")
    
    def process_queue(self):
        global is_running
        
        while self.is_running:
            try:
                # Get email from queue
                email_data = email_queue.get(timeout=1)
                
                # Send email
                success = self.send_email(**email_data)
                
                # Update campaign statistics
                if email_data.get('campaign_id'):
                    self.update_campaign_stats(email_data['campaign_id'], success)
                
                # Delay between emails
                time.sleep(DELAY_BETWEEN_EMAILS)
                
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Queue processing error: {e}")
    
    def update_campaign_stats(self, campaign_id, success):
        try:
            conn = sqlite3.connect('email_campaigns.db')
            cursor = conn.cursor()
            
            if success:
                cursor.execute('''
                    UPDATE campaigns 
                    SET sent_emails = sent_emails + 1 
                    WHERE id = ?
                ''', (campaign_id,))
            else:
                cursor.execute('''
                    UPDATE campaigns 
                    SET failed_emails = failed_emails + 1 
                    WHERE id = ?
                ''', (campaign_id,))
            
            # Check if campaign is complete
            cursor.execute('''
                SELECT total_emails, sent_emails, failed_emails 
                FROM campaigns 
                WHERE id = ?
            ''', (campaign_id,))
            
            result = cursor.fetchone()
            if result:
                total, sent, failed = result
                if sent + failed >= total:
                    cursor.execute('''
                        UPDATE campaigns 
                        SET status = 'completed', completed_at = ? 
                        WHERE id = ?
                    ''', (datetime.now(), campaign_id))
            
            conn.commit()
            conn.close()
        except Exception as e:
            logging.error(f"Failed to update campaign stats: {e}")

# Global email sender instance
sender = EmailSender()

# Web interface HTML template
WEB_INTERFACE = '''
<!DOCTYPE html>
<html>
<head>
    <title>VPS Email Sender</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .stats { display: flex; justify-content: space-around; }
        .stat { text-align: center; }
        .stat h3 { margin: 0; color: #333; }
        .stat p { margin: 5px 0; font-size: 24px; font-weight: bold; color: #007cba; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background: #007cba; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        textarea { width: 100%; height: 100px; }
        input[type="text"], input[type="email"] { width: 100%; padding: 8px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        .status { padding: 10px; border-radius: 3px; margin: 10px 0; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>VPS Email Sender Dashboard</h1>
        
        <div class="section">
            <h2>System Status</h2>
            <div class="stats">
                <div class="stat">
                    <h3>Queue Size</h3>
                    <p id="queue-size">0</p>
                </div>
                <div class="stat">
                    <h3>Sent Today</h3>
                    <p id="sent-count">0</p>
                </div>
                <div class="stat">
                    <h3>Failed Today</h3>
                    <p id="failed-count">0</p>
                </div>
                <div class="stat">
                    <h3>Status</h3>
                    <p id="sender-status">Stopped</p>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn-success" onclick="startSender()">Start Sender</button>
                <button class="btn-danger" onclick="stopSender()">Stop Sender</button>
                <button class="btn-secondary" onclick="updateStatus()">Refresh Status</button>
            </div>
        </div>
        
        <div class="section">
            <h2>Send Single Email</h2>
            <div class="form-group">
                <label>To Email:</label>
                <input type="email" id="single-to" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label>Subject:</label>
                <input type="text" id="single-subject" placeholder="Email subject">
            </div>
            <div class="form-group">
                <label>Message:</label>
                <textarea id="single-body" placeholder="Email content (HTML supported)"></textarea>
            </div>
            <button class="btn-primary" onclick="sendSingleEmail()">Send Email</button>
        </div>
        
        <div class="section">
            <h2>Bulk Email Campaign</h2>
            <div class="form-group">
                <label>Campaign Name:</label>
                <input type="text" id="campaign-name" placeholder="My Campaign">
            </div>
            <div class="form-group">
                <label>Subject:</label>
                <input type="text" id="bulk-subject" placeholder="Email subject">
            </div>
            <div class="form-group">
                <label>Message (use {{email}}, {{name}} for personalization):</label>
                <textarea id="bulk-body" placeholder="Hello {{name}}, this is a personalized email to {{email}}"></textarea>
            </div>
            <div class="form-group">
                <label>Recipients (CSV format: email,name):</label>
                <textarea id="bulk-recipients" placeholder="<EMAIL>,John Doe
<EMAIL>,Jane Smith"></textarea>
            </div>
            <button class="btn-primary" onclick="sendBulkEmails()">Start Campaign</button>
        </div>
        
        <div id="message-area"></div>
    </div>
    
    <script>
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => messageArea.innerHTML = '', 5000);
        }
        
        function updateStatus() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('queue-size').textContent = data.queue_size;
                    document.getElementById('sent-count').textContent = data.sent_count;
                    document.getElementById('failed-count').textContent = data.failed_count;
                    document.getElementById('sender-status').textContent = data.is_running ? 'Running' : 'Stopped';
                });
        }
        
        function startSender() {
            fetch('/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    showMessage('Email sender started', 'success');
                    updateStatus();
                });
        }
        
        function stopSender() {
            fetch('/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    showMessage('Email sender stopped', 'info');
                    updateStatus();
                });
        }
        
        function sendSingleEmail() {
            const emailData = {
                to_email: document.getElementById('single-to').value,
                subject: document.getElementById('single-subject').value,
                body: document.getElementById('single-body').value
            };
            
            fetch('/send-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(emailData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showMessage('Error: ' + data.error, 'error');
                } else {
                    showMessage('Email queued successfully', 'success');
                    updateStatus();
                }
            });
        }
        
        function sendBulkEmails() {
            const campaignName = document.getElementById('campaign-name').value;
            const subject = document.getElementById('bulk-subject').value;
            const body = document.getElementById('bulk-body').value;
            const recipientsText = document.getElementById('bulk-recipients').value;
            
            const recipients = recipientsText.split('\\n').map(line => {
                const [email, name] = line.split(',').map(s => s.trim());
                return { email, name: name || email };
            }).filter(r => r.email);
            
            const campaignData = {
                campaign_name: campaignName,
                subject: subject,
                body: body,
                recipients: recipients
            };
            
            fetch('/send-campaign', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(campaignData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showMessage('Error: ' + data.error, 'error');
                } else {
                    showMessage(`Campaign started with ${data.emails_queued} emails`, 'success');
                    updateStatus();
                }
            });
        }
        
        // Update status every 5 seconds
        setInterval(updateStatus, 5000);
        updateStatus();
    </script>
</body>
</html>
'''

# Routes
@app.route('/')
def dashboard():
    return render_template_string(WEB_INTERFACE)

@app.route('/send-email', methods=['POST'])
def send_email():
    data = request.json
    
    required_fields = ['to_email', 'subject', 'body']
    if not all(field in data for field in required_fields):
        return jsonify({'error': 'Missing required fields'}), 400
    
    # Add to queue
    email_queue.put({
        'to_email': data['to_email'],
        'subject': data['subject'],
        'body': data['body'],
        'from_email': data.get('from_email', DEFAULT_FROM_EMAIL)
    })
    
    return jsonify({'status': 'queued', 'queue_size': email_queue.qsize()})

@app.route('/send-campaign', methods=['POST'])
def send_campaign():
    data = request.json
    
    required_fields = ['campaign_name', 'subject', 'body', 'recipients']
    if not all(field in data for field in required_fields):
        return jsonify({'error': 'Missing required fields'}), 400
    
    # Create campaign
    campaign_id = str(uuid.uuid4())
    
    # Save campaign to database
    conn = sqlite3.connect('email_campaigns.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO campaigns (id, name, subject, body, total_emails, sent_emails, failed_emails, status, created_at)
        VALUES (?, ?, ?, ?, ?, 0, 0, 'active', ?)
    ''', (campaign_id, data['campaign_name'], data['subject'], data['body'], len(data['recipients']), datetime.now()))
    
    conn.commit()
    conn.close()
    
    # Queue emails
    count = 0
    for recipient in data['recipients']:
        if 'email' in recipient:
            # Personalize content
            personalized_subject = data['subject'].replace('{{name}}', recipient.get('name', '')).replace('{{email}}', recipient['email'])
            personalized_body = data['body'].replace('{{name}}', recipient.get('name', '')).replace('{{email}}', recipient['email'])
            
            email_queue.put({
                'to_email': recipient['email'],
                'subject': personalized_subject,
                'body': personalized_body,
                'from_email': data.get('from_email', DEFAULT_FROM_EMAIL),
                'campaign_id': campaign_id
            })
            count += 1
    
    return jsonify({'status': 'queued', 'emails_queued': count, 'campaign_id': campaign_id, 'queue_size': email_queue.qsize()})

@app.route('/status', methods=['GET'])
def status():
    global sent_count, failed_count
    return jsonify({
        'queue_size': email_queue.qsize(),
        'is_running': sender.is_running,
        'sent_count': sent_count,
        'failed_count': failed_count
    })

@app.route('/start', methods=['POST'])
def start_sender():
    if not sender.is_running:
        sender.is_running = True
        thread = threading.Thread(target=sender.process_queue)
        thread.daemon = True
        thread.start()
        return jsonify({'status': 'started'})
    return jsonify({'status': 'already_running'})

@app.route('/stop', methods=['POST'])
def stop_sender():
    sender.is_running = False
    return jsonify({'status': 'stopped'})

@app.route('/campaigns', methods=['GET'])
def get_campaigns():
    conn = sqlite3.connect('email_campaigns.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, name, subject, total_emails, sent_emails, failed_emails, status, created_at
        FROM campaigns
        ORDER BY created_at DESC
    ''')
    
    campaigns = []
    for row in cursor.fetchall():
        campaigns.append({
            'id': row[0],
            'name': row[1],
            'subject': row[2],
            'total_emails': row[3],
            'sent_emails': row[4],
            'failed_emails': row[5],
            'status': row[6],
            'created_at': row[7]
        })
    
    conn.close()
    return jsonify(campaigns)

if __name__ == '__main__':
    # Initialize database
    init_db()
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('email_server.log'),
            logging.StreamHandler()
        ]
    )
    
    print("Starting VPS Email Server...")
    print("Dashboard will be available at: http://YOUR_VPS_IP:5000")
    print("Make sure to configure your SMTP settings in the script!")
    
    # Start the Flask app
    app.run(host='0.0.0.0', port=5000, debug=False)
