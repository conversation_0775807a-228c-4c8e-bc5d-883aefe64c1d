import sys
import os
import pandas as pd
import math
import csv

def convert_and_split(input_file, output_prefix="split", lines_per_file=500):
    """
    Convert an Excel file to CSV and split it into multiple smaller CSV files.
    
    Args:
        input_file: Path to the input Excel file
        output_prefix: Prefix for output files (will be followed by a number)
        lines_per_file: Number of lines per output file (default: 500)
    """
    try:
        # Check if input file exists
        if not os.path.exists(input_file):
            print(f"Error: Input file '{input_file}' not found.")
            return False
        
        # Get the directory of the input file for output files
        output_dir = os.path.dirname(input_file)
        if output_dir == "":
            output_dir = "."
        
        print(f"Reading Excel file: {input_file}")
        
        # Read the Excel file
        df = pd.read_excel(input_file)
        
        # Get total number of rows and columns
        total_rows = len(df)
        columns = df.columns.tolist()
        
        print(f"Excel file loaded successfully!")
        print(f"Total rows: {total_rows:,}")
        print(f"Columns: {', '.join(columns)}")
        
        # Calculate number of output files
        num_files = math.ceil(total_rows / lines_per_file)
        print(f"Will create {num_files:,} files with {lines_per_file} rows per file (plus header)")
        
        # Split the dataframe and save to multiple CSV files
        for i in range(num_files):
            start_idx = i * lines_per_file
            end_idx = min((i + 1) * lines_per_file, total_rows)
            
            # Create a slice of the dataframe
            df_slice = df.iloc[start_idx:end_idx]
            
            # Create output filename
            file_number = i + 1
            output_file = f"{output_dir}/{output_prefix}_{file_number}.csv"
            
            # Save to CSV
            df_slice.to_csv(output_file, index=False)
            
            print(f"Created file: {output_file} with {len(df_slice):,} rows")
        
        print(f"\nConversion and splitting complete!")
        print(f"Created {num_files:,} CSV files with prefix '{output_prefix}_'")
        return True
    
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python convert_and_split.py input.xlsx [output_prefix] [lines_per_file]")
        print("\nExample: python convert_and_split.py data.xlsx split_data 500")
        print("\nDefaults:")
        print("  - output_prefix: 'split'")
        print("  - lines_per_file: 500")
        return
    
    input_file = sys.argv[1]
    output_prefix = sys.argv[2] if len(sys.argv) > 2 else "split"
    
    try:
        lines_per_file = int(sys.argv[3]) if len(sys.argv) > 3 else 500
    except ValueError:
        print(f"Error: lines_per_file must be a number. Using default value of 500.")
        lines_per_file = 500
    
    print(f"Input file: {input_file}")
    print(f"Output prefix: {output_prefix}")
    print(f"Lines per file: {lines_per_file}")
    
    convert_and_split(input_file, output_prefix, lines_per_file)

if __name__ == "__main__":
    main()
