#!/usr/bin/env python3
"""
Debug version of Excel splitter with detailed error reporting
"""

import os
import sys

def check_environment():
    """Check if everything is set up correctly"""
    print("🔍 Diagnostic Check")
    print("=" * 40)
    
    # Check Python version
    print(f"🐍 Python version: {sys.version}")
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"📁 Current directory: {current_dir}")
    
    # List all files in current directory
    print(f"\n📋 Files in current directory:")
    files = os.listdir(".")
    for file in files:
        size = os.path.getsize(file) if os.path.isfile(file) else "folder"
        print(f"   • {file} ({size} bytes)" if size != "folder" else f"   📁 {file}/")
    
    # Check for Excel files specifically
    excel_files = [f for f in files if f.endswith(('.xlsx', '.xls', '.xlsm'))]
    print(f"\n📊 Excel files found: {len(excel_files)}")
    for excel_file in excel_files:
        print(f"   • {excel_file}")
    
    # Check if pandas is available
    try:
        import pandas as pd
        print(f"✅ Pandas is installed (version: {pd.__version__})")
    except ImportError:
        print(f"❌ Pandas is NOT installed")
        return False
    
    # Check if openpyxl is available
    try:
        import openpyxl
        print(f"✅ Openpyxl is installed (version: {openpyxl.__version__})")
    except ImportError:
        print(f"❌ Openpyxl is NOT installed")
        return False
    
    return True

def find_excel_file():
    """Find the Excel file to process"""
    print(f"\n🔍 Looking for Excel file...")
    
    # First, look for the specific file
    target_file = "Fresh December9.5k.xlsx"
    if os.path.exists(target_file):
        print(f"✅ Found target file: {target_file}")
        return target_file
    
    # If not found, look for any Excel files
    excel_files = [f for f in os.listdir(".") if f.endswith(('.xlsx', '.xls', '.xlsm'))]
    
    if not excel_files:
        print(f"❌ No Excel files found!")
        print(f"💡 Please make sure your Excel file is in this folder:")
        print(f"   {os.getcwd()}")
        return None
    
    if len(excel_files) == 1:
        print(f"✅ Found Excel file: {excel_files[0]}")
        return excel_files[0]
    
    # Multiple files found
    print(f"📋 Multiple Excel files found:")
    for i, file in enumerate(excel_files, 1):
        print(f"   {i}. {file}")
    
    while True:
        try:
            choice = input(f"Select file (1-{len(excel_files)}): ")
            index = int(choice) - 1
            if 0 <= index < len(excel_files):
                return excel_files[index]
            else:
                print("❌ Invalid choice!")
        except ValueError:
            print("❌ Please enter a number!")

def test_excel_reading(excel_file):
    """Test if we can read the Excel file"""
    print(f"\n📖 Testing Excel file reading...")
    
    try:
        import pandas as pd
        
        print(f"🔄 Attempting to read: {excel_file}")
        df = pd.read_excel(excel_file)
        
        print(f"✅ Successfully read Excel file!")
        print(f"📊 Rows: {len(df)}")
        print(f"📊 Columns: {len(df.columns)}")
        print(f"📊 Column names: {list(df.columns)}")
        
        if len(df) > 0:
            print(f"\n📋 First few rows:")
            print(df.head(3).to_string())
        
        return df
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")
        print(f"💡 Possible solutions:")
        print(f"   • Make sure the file is not open in Excel")
        print(f"   • Check if the file is corrupted")
        print(f"   • Try installing xlrd: pip install xlrd")
        return None

def create_test_csv(df, excel_file):
    """Create test CSV files"""
    print(f"\n📁 Creating CSV files...")
    
    try:
        import pandas as pd
        import math
        from pathlib import Path
        
        rows_per_file = 200
        total_files = math.ceil(len(df) / rows_per_file)
        
        # Create output folder
        output_folder = "csv_email_lists"
        Path(output_folder).mkdir(exist_ok=True)
        print(f"📁 Created folder: {output_folder}")
        
        # Split and save
        for i in range(total_files):
            start_row = i * rows_per_file
            end_row = min((i + 1) * rows_per_file, len(df))
            
            chunk = df.iloc[start_row:end_row]
            
            filename = f"Fresh_December9.5k_part_{i+1:03d}.csv"
            filepath = os.path.join(output_folder, filename)
            
            chunk.to_csv(filepath, index=False)
            
            print(f"✅ Created: {filename} ({len(chunk)} rows)")
        
        print(f"\n🎉 Successfully created {total_files} CSV files!")
        
        # Verify files were created
        print(f"\n🔍 Verifying created files:")
        for i in range(total_files):
            filename = f"Fresh_December9.5k_part_{i+1:03d}.csv"
            filepath = os.path.join(output_folder, filename)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                print(f"   ✅ {filename} ({size:,} bytes)")
            else:
                print(f"   ❌ {filename} (NOT FOUND)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating CSV files: {e}")
        return False

def main():
    print("🚀 Excel to CSV Splitter - Debug Mode")
    print("=" * 50)
    
    # Step 1: Check environment
    if not check_environment():
        print(f"\n❌ Environment check failed!")
        print(f"💡 Please install required packages:")
        print(f"   pip install pandas openpyxl")
        return
    
    # Step 2: Find Excel file
    excel_file = find_excel_file()
    if not excel_file:
        return
    
    # Step 3: Test reading Excel file
    df = test_excel_reading(excel_file)
    if df is None:
        return
    
    # Step 4: Create CSV files
    success = create_test_csv(df, excel_file)
    
    if success:
        print(f"\n✅ All done! Check the 'csv_email_lists' folder for your files.")
    else:
        print(f"\n❌ Failed to create CSV files.")

if __name__ == "__main__":
    main()
