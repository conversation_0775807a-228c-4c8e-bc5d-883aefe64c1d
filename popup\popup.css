/* MailSuite Popup Styles */
body {
  font-family: 'Google Sans', Roboto, Arial, sans-serif;
  width: 380px;
  min-height: 500px;
  margin: 0;
  padding: 0;
  color: #202124;
  background: #fff;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #1a73e8, #4285f4);
  color: white;
}

.header h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
}

.version {
  font-size: 12px;
  opacity: 0.8;
}

/* Tabs */
.tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dadce0;
}

.tab {
  flex: 1;
  background: none;
  border: none;
  padding: 12px 8px;
  cursor: pointer;
  font-size: 12px;
  color: #5f6368;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab:hover {
  color: #1a73e8;
  background: #e8f0fe;
}

.tab.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
  background: white;
  font-weight: 500;
}

/* Tab Content */
.tab-content {
  display: none;
  padding: 16px 20px;
  flex: 1;
  overflow-y: auto;
}

.tab-content.active {
  display: block;
}

/* Quick Stats */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 8px;
  padding: 16px 12px;
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 500;
  color: #1a73e8;
}

.stat-card p {
  margin: 0;
  font-size: 12px;
  color: #5f6368;
}

/* Active Campaign */
.active-campaign {
  background: #e8f0fe;
  border: 1px solid #1a73e8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.active-campaign h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #1a73e8;
}

.campaign-subject {
  font-weight: 500;
  margin-bottom: 12px;
  color: #202124;
}

.campaign-progress {
  margin-bottom: 12px;
}

.progress-bar {
  background: #dadce0;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  background: #1a73e8;
  height: 100%;
  width: 0%;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #5f6368;
}

.campaign-actions {
  display: flex;
  gap: 8px;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: 12px;
}

/* Buttons */
.btn-primary {
  background: #1a73e8;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  flex: 1;
}

.btn-primary:hover {
  background: #1765cc;
}

.btn-secondary {
  background: #f8f9fa;
  color: #1a73e8;
  border: 1px solid #dadce0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #e8f0fe;
}

.btn-danger {
  background: #ea4335;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-danger:hover {
  background: #d93025;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  color: #202124;
}

/* Campaigns List */
.campaigns-list {
  max-height: 300px;
  overflow-y: auto;
}

.campaign-item {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.campaign-item h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #202124;
}

.campaign-item p {
  margin: 2px 0;
  font-size: 12px;
  color: #5f6368;
}

.campaign-status {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.campaign-status.active {
  background: #e8f5e8;
  color: #137333;
}

.campaign-status.completed {
  background: #e3f2fd;
  color: #1565c0;
}

.campaign-status.paused {
  background: #fff3e0;
  color: #ef6c00;
}

.campaign-status.stopped {
  background: #ffebee;
  color: #c62828;
}

/* Analytics */
.analytics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.analytics-card {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 8px;
  padding: 16px;
}

.analytics-card h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #202124;
}

.metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 13px;
  color: #5f6368;
}

.metric-value {
  font-size: 13px;
  font-weight: 500;
  color: #202124;
}

.activity-list {
  max-height: 150px;
  overflow-y: auto;
}

.activity-item {
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
  font-size: 12px;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  color: #5f6368;
  font-size: 11px;
}

/* Settings */
.settings-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.settings-section:last-child {
  border-bottom: none;
}

.settings-section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #202124;
}

.setting-item {
  margin-bottom: 12px;
}

.setting-item label {
  display: block;
  font-size: 13px;
  margin-bottom: 4px;
  color: #202124;
}

.setting-item input[type="text"],
.setting-item input[type="number"],
.setting-item select {
  width: 100%;
  padding: 8px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 13px;
  box-sizing: border-box;
}

.setting-item input[type="checkbox"] {
  margin-right: 8px;
}

.setting-item small {
  display: block;
  font-size: 11px;
  color: #5f6368;
  margin-top: 4px;
}

.setting-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.setting-actions button {
  flex: 1;
  min-width: 100px;
}

/* Footer */
.footer {
  margin-top: auto;
  padding: 12px 20px;
  border-top: 1px solid #dadce0;
  background: #f8f9fa;
}

.footer-links {
  display: flex;
  gap: 16px;
  margin-bottom: 4px;
}

.footer-links a {
  font-size: 12px;
  color: #1a73e8;
  text-decoration: none;
}

.footer-links a:hover {
  text-decoration: underline;
}

.footer-text {
  font-size: 11px;
  color: #5f6368;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
