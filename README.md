# MailSuite Clone - Gmail Automation Extension

A comprehensive Gmail automation extension similar to MailSuite, designed for high-volume email campaigns with advanced features.

## Features

### Core Functionality
- **Mail Merge**: Upload CSV files and send personalized emails to thousands of recipients
- **Subdomain Rotation**: Automatically rotate through multiple sending domains to improve deliverability
- **Email Templates**: Save and reuse email templates with variable placeholders
- **Email Tracking**: Track opens and clicks with detailed analytics
- **Campaign Management**: Monitor active campaigns with real-time progress tracking

### Advanced Features
- **Scheduled Sending**: Schedule campaigns for future delivery
- **Analytics Dashboard**: Comprehensive reporting on email performance
- **Unsubscribe Management**: Automatic unsubscribe link insertion
- **Batch Processing**: Intelligent batching to respect Gmail's sending limits
- **Variable Personalization**: Dynamic content insertion based on recipient data

## Installation

1. Download or clone this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top-right corner
4. Click "Load unpacked" and select the extension folder
5. The extension will appear in your Chrome toolbar

## Usage

### Setting Up Your First Campaign

1. **Open Gmail** and click the "MailSuite" button in the toolbar
2. **Create a new compose window** - the MailSuite panel will appear automatically
3. **Upload your contacts** in CSV format with columns like: firstName, lastName, email, company
4. **Write your email** using variables like {{firstName}}, {{company}}, etc.
5. **Configure sending options**:
   - Enable subdomain rotation if you have multiple domains
   - Set up tracking preferences
   - Choose batch size and delays
6. **Start your campaign** and monitor progress in the popup dashboard

### CSV Format

Your CSV file should have headers in the first row. Common headers include:
- firstName, lastName, email (required)
- company, phone, website
- Any custom fields you want to use in your emails

Example:
```csv
firstName,lastName,email,company
John,Doe,<EMAIL>,Acme Corp
Jane,Smith,<EMAIL>,Tech Inc
```

### Subdomain Rotation Setup

To use subdomain rotation for better deliverability:

1. Set up multiple subdomains (mail1, mail2, mail3, etc.)
2. Configure SPF, DKIM, and DMARC records for each subdomain
3. Add the subdomains to Gmail as verified sending addresses
4. Enable subdomain rotation in the extension settings

## For 10K Emails Per Day

To send 10,000 emails per day, you'll need:

### 1. Multiple Gmail Accounts
- Create 20+ Gmail accounts
- Each account can send ~500 emails/day
- Use the extension across multiple accounts

### 2. Proper Domain Setup
- Register multiple domains or subdomains
- Set up proper email authentication (SPF, DKIM, DMARC)
- Warm up your domains gradually

### 3. Email Service Integration
- Consider using Amazon SES for higher volume
- Set up proper tracking infrastructure
- Implement bounce and complaint handling

### 4. Content and List Quality
- Maintain clean email lists
- Use engaging, non-spammy content
- Include proper unsubscribe mechanisms
- Monitor sender reputation

## Technical Requirements

### For the Extension
- Chrome browser
- Gmail account(s)
- CSV files with contact data

### For High-Volume Sending
- Multiple domains/subdomains
- Email authentication setup
- Tracking server (optional)
- Clean, opted-in email lists

## Configuration

### Tracking Setup
To enable email tracking, you'll need to set up a tracking server:

1. Set up a domain for tracking (e.g., track.yourdomain.com)
2. Create endpoints for:
   - `/track/open/{campaignId}/{recipientId}` - for open tracking
   - `/track/click/{campaignId}/{recipientId}?url=...` - for click tracking
   - `/unsubscribe/{campaignId}/{recipientId}` - for unsubscribes

3. Update the tracking domain in the extension settings

### Gmail API (Optional)
For advanced features, you can integrate with Gmail API:
- Higher sending limits
- Better error handling
- Advanced scheduling

## Best Practices

### Deliverability
1. **Warm up new domains** gradually (start with 50-100 emails/day)
2. **Use consistent sending patterns** (same time, frequency)
3. **Monitor blacklists** and sender reputation
4. **Maintain list hygiene** (remove bounces, complaints)

### Content
1. **Personalize emails** beyond just names
2. **Avoid spam trigger words** and excessive promotional language
3. **Include clear unsubscribe options**
4. **Use proper HTML structure** and alt text for images

### Technical
1. **Respect Gmail's limits** (500 emails/day per account)
2. **Use delays between emails** (30-60 seconds minimum)
3. **Monitor for errors** and handle them gracefully
4. **Keep detailed logs** for troubleshooting

## Troubleshooting

### Common Issues

**Extension not loading:**
- Check that all files are in the correct folders
- Verify manifest.json syntax
- Check Chrome developer console for errors

**Emails going to spam:**
- Verify domain authentication (SPF, DKIM, DMARC)
- Check content for spam triggers
- Ensure proper list opt-in
- Monitor sender reputation

**Gmail blocking sends:**
- Reduce sending volume
- Add delays between emails
- Use multiple accounts
- Check for account restrictions

### Support

For issues or questions:
1. Check the Chrome extension console for errors
2. Verify your CSV format matches requirements
3. Ensure proper domain setup for high-volume sending
4. Test with small batches before scaling up

## Legal Considerations

- Only send emails to opted-in recipients
- Include proper unsubscribe mechanisms
- Comply with CAN-SPAM, GDPR, and other regulations
- Respect recipient preferences and complaints
- Maintain proper records and consent documentation

## Disclaimer

This extension is for legitimate email marketing purposes only. Users are responsible for:
- Obtaining proper consent from recipients
- Complying with applicable laws and regulations
- Maintaining good sending practices
- Respecting Gmail's terms of service

Use responsibly and ethically!
