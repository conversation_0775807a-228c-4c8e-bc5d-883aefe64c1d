# 📧 MailSuite Clone - Complete Installation Guide

## 🎯 Overview
This guide will help you set up a complete email automation system using your VPS for unlimited email sending.

## 📋 Prerequisites
- ✅ VPS server (Ubuntu 20.04+ recommended)
- ✅ Domain name
- ✅ Chrome browser
- ✅ Basic command line knowledge

---

## 🖥️ PHASE 1: VPS Email Server Setup

### Step 1: Connect to Your VPS
```bash
# Connect via SSH
ssh root@YOUR_VPS_IP

# Update system
apt update && apt upgrade -y
```

### Step 2: Install Email Server
```bash
# Download installation script
curl -O https://raw.githubusercontent.com/your-files/install_vps_email.sh

# Make executable
chmod +x install_vps_email.sh

# Run installation (will prompt for domain and IP)
sudo ./install_vps_email.sh
```

**You'll be asked for:**
- Domain name: `yourdomain.com`
- VPS IP address: `123.456.789.123`

### Step 3: Configure DNS Records
Add these records to your domain DNS:

```dns
# Required DNS Records
Type    Name                    Value                           TTL
A       mail.yourdomain.com     YOUR_VPS_IP                    300
MX      yourdomain.com          10 mail.yourdomain.com         300
TXT     yourdomain.com          "v=spf1 ip4:YOUR_VPS_IP ~all"  300
TXT     _dmarc.yourdomain.com   "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>" 300
```

### Step 4: Set Up Reverse DNS
Contact your VPS provider to set up PTR record:
```
PTR: YOUR_VPS_IP → mail.yourdomain.com
```

### Step 5: Test Email Server
```bash
# Check services
systemctl status postfix
systemctl status email-server

# Send test email
echo "Test message" | mail -s "Test Subject" <EMAIL>

# Check logs
tail -f /var/log/mail.log
```

### Step 6: Access Web Dashboard
Open: `http://YOUR_VPS_IP:5000`

---

## 🌐 PHASE 2: Chrome Extension Setup

### Step 7: Install Extension
1. Download extension files to your computer
2. Open Chrome → `chrome://extensions/`
3. Enable "Developer mode" (top right)
4. Click "Load unpacked"
5. Select the extension folder
6. Extension should appear in Chrome toolbar

### Step 8: Test Extension
1. Go to `https://mail.google.com`
2. Look for "MailSuite" in Gmail sidebar
3. Click "Compose" → Look for MailSuite button in toolbar
4. Click MailSuite button → Panel should open

---

## 📊 PHASE 3: First Email Campaign

### Step 9: Prepare Your Email List
Create a CSV file with this format:
```csv
firstName,lastName,email,company
John,Doe,<EMAIL>,Acme Corp
Jane,Smith,<EMAIL>,Tech Inc
Bob,Johnson,<EMAIL>,Innovation LLC
```

### Step 10: Configure Extension for VPS
1. Open MailSuite panel in Gmail compose
2. Go to "Settings" tab
3. Set VPS URL: `http://YOUR_VPS_IP:5000`
4. Enable "Use VPS Server" option

### Step 11: Send First Campaign
1. In Gmail, click "Compose"
2. Click "MailSuite" button
3. Go to "Mail Merge" tab
4. Upload your CSV file
5. Write subject: `Hello {{firstName}}!`
6. Write message: `Hi {{firstName}}, this is a test email for {{company}}.`
7. Click "Start Mail Merge"

---

## 🔧 PHASE 4: Advanced Configuration

### Step 12: Improve Deliverability
```bash
# On your VPS, configure DKIM
cd /etc/opendkim/keys/yourdomain.com
cat default.txt

# Add the DKIM record to your DNS:
# default._domainkey.yourdomain.com TXT "v=DKIM1; k=rsa; p=YOUR_PUBLIC_KEY"
```

### Step 13: Monitor Performance
- Web Dashboard: `http://YOUR_VPS_IP:5000`
- Email logs: `tail -f /var/log/mail.log`
- Queue status: `mailq`

### Step 14: Scale Up Sending
```bash
# Increase sending limits in /opt/email-server/email_server.py
# Modify these variables:
MAX_EMAILS_PER_HOUR = 2000
DELAY_BETWEEN_EMAILS = 1.5
```

---

## 🚨 Troubleshooting

### Common Issues:

**1. Emails going to spam:**
- Check SPF, DKIM, DMARC records
- Warm up your IP gradually (start with 100/day)
- Use proper from names and subjects

**2. Extension not showing:**
- Refresh Gmail page
- Check Chrome console for errors
- Reload extension in chrome://extensions/

**3. VPS connection failed:**
- Check firewall: `ufw allow 5000`
- Verify service: `systemctl status email-server`
- Check logs: `journalctl -u email-server`

**4. DNS not propagating:**
- Wait 24-48 hours for full propagation
- Use DNS checker tools online
- Verify with: `dig MX yourdomain.com`

---

## 📈 Scaling to 10K+ Emails/Day

### Week 1: Start Small
- Send 100 emails/day
- Monitor deliverability
- Check spam rates

### Week 2: Gradual Increase
- Increase to 500 emails/day
- Monitor sender reputation
- Adjust if needed

### Week 3: Scale Up
- Increase to 2,000 emails/day
- Monitor all metrics
- Fine-tune settings

### Week 4+: Full Scale
- Send 10,000+ emails/day
- Maintain good practices
- Monitor continuously

---

## 🎯 Success Metrics

**Good Deliverability:**
- ✅ Inbox rate: >90%
- ✅ Spam rate: <5%
- ✅ Bounce rate: <2%
- ✅ Open rate: >20%

**Warning Signs:**
- ❌ High spam complaints
- ❌ IP blacklisting
- ❌ Low engagement rates
- ❌ High bounce rates

---

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section
2. Review logs on your VPS
3. Test with small batches first
4. Monitor deliverability closely

Remember: Start small, monitor closely, scale gradually! 🚀
