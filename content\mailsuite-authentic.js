// Authentic MailSuite Clone - Exact UI/UX Replica
class AuthenticMailSuite {
  constructor() {
    this.isActive = false;
    this.currentCompose = null;
    this.campaigns = new Map();
    this.init();
  }

  init() {
    console.log('MailSuite: Initializing authentic experience...');
    this.waitForGmail(() => {
      this.injectMailSuiteUI();
      this.observeComposeWindows();
      this.setupGlobalListeners();
    });
  }

  waitForGmail(callback) {
    const checkGmail = () => {
      if (document.querySelector('.nH') && document.querySelector('div[role="main"]')) {
        callback();
      } else {
        setTimeout(checkGmail, 1000);
      }
    };
    checkGmail();
  }

  injectMailSuiteUI() {
    // Add MailSuite to Gmail's left sidebar (like real MailSuite)
    this.addSidebarButton();
    
    // Add MailSuite compose enhancement
    this.observeComposeWindows();
  }

  addSidebarButton() {
    const sidebar = document.querySelector('div[role="navigation"]');
    if (!sidebar || document.getElementById('mailsuite-sidebar')) return;

    const mailsuiteItem = document.createElement('div');
    mailsuiteItem.id = 'mailsuite-sidebar';
    mailsuiteItem.className = 'TK';
    mailsuiteItem.innerHTML = `
      <div class="TO mailsuite-sidebar-item" role="menuitem" tabindex="-1">
        <div class="TN">
          <div class="n3 aBU" style="background-color: #4285f4; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
            <span style="color: white; font-size: 12px; font-weight: bold;">M</span>
          </div>
        </div>
        <div class="TL">
          <div class="TO mailsuite-text">MailSuite</div>
        </div>
      </div>
    `;

    mailsuiteItem.addEventListener('click', () => this.openMailSuiteDashboard());
    
    // Insert after Inbox in sidebar
    const inboxItem = sidebar.querySelector('div[data-tooltip="Inbox"]');
    if (inboxItem && inboxItem.parentNode) {
      inboxItem.parentNode.insertBefore(mailsuiteItem, inboxItem.nextSibling);
    }
  }

  observeComposeWindows() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1) {
            // Check for new compose window
            const composeWindow = node.querySelector && node.querySelector('.M9');
            if (composeWindow) {
              setTimeout(() => this.enhanceComposeWindow(composeWindow), 500);
            }
            
            // Check if node itself is compose window
            if (node.classList && node.classList.contains('M9')) {
              setTimeout(() => this.enhanceComposeWindow(node), 500);
            }
          }
        });
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });
    
    // Check existing compose windows
    document.querySelectorAll('.M9').forEach(compose => {
      this.enhanceComposeWindow(compose);
    });
  }

  enhanceComposeWindow(composeWindow) {
    if (composeWindow.querySelector('.mailsuite-enhanced') || !composeWindow.querySelector('.Am.Al.editable')) {
      return;
    }

    // Mark as enhanced
    const marker = document.createElement('div');
    marker.className = 'mailsuite-enhanced';
    marker.style.display = 'none';
    composeWindow.appendChild(marker);

    // Add MailSuite button to compose toolbar (like real MailSuite)
    this.addComposeToolbarButton(composeWindow);
  }

  addComposeToolbarButton(composeWindow) {
    const toolbar = composeWindow.querySelector('.gU.Up .J-J5-Ji');
    if (!toolbar) return;

    const mailsuiteBtn = document.createElement('div');
    mailsuiteBtn.className = 'T-I J-J5-Ji aoO v7 T-I-atl L3 mailsuite-compose-btn';
    mailsuiteBtn.setAttribute('role', 'button');
    mailsuiteBtn.setAttribute('tabindex', '0');
    mailsuiteBtn.setAttribute('data-tooltip', 'MailSuite - Mail Merge & Tracking');
    
    mailsuiteBtn.innerHTML = `
      <div class="asa">
        <span class="mailsuite-btn-icon" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNEgyMFY2SDRWNFpNNCA4SDE2VjEwSDRWOFpNNCA4SDE2VjEwSDRWOFpNNCA4SDE2VjEwSDRWOFoiIGZpbGw9IiM1ZjYzNjgiLz4KPC9zdmc+'); width: 16px; height: 16px; display: inline-block;"></span>
        <span class="mailsuite-btn-text" style="margin-left: 4px;">MailSuite</span>
      </div>
    `;

    mailsuiteBtn.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.toggleMailSuitePanel(composeWindow);
    });

    // Insert button in toolbar
    toolbar.appendChild(mailsuiteBtn);
  }

  toggleMailSuitePanel(composeWindow) {
    let panel = composeWindow.querySelector('.mailsuite-panel');
    
    if (panel) {
      // Toggle existing panel
      panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    } else {
      // Create new panel
      this.createMailSuitePanel(composeWindow);
    }
  }

  createMailSuitePanel(composeWindow) {
    const panel = document.createElement('div');
    panel.className = 'mailsuite-panel';
    panel.innerHTML = `
      <div class="mailsuite-panel-header">
        <div class="mailsuite-logo">
          <span class="mailsuite-icon">📧</span>
          <span class="mailsuite-title">MailSuite</span>
        </div>
        <div class="mailsuite-panel-controls">
          <button class="mailsuite-minimize" title="Minimize">−</button>
          <button class="mailsuite-close" title="Close">×</button>
        </div>
      </div>
      
      <div class="mailsuite-panel-body">
        <div class="mailsuite-tabs">
          <div class="mailsuite-tab active" data-tab="mailmerge">
            <span class="mailsuite-tab-icon">📊</span>
            <span>Mail Merge</span>
          </div>
          <div class="mailsuite-tab" data-tab="templates">
            <span class="mailsuite-tab-icon">📝</span>
            <span>Templates</span>
          </div>
          <div class="mailsuite-tab" data-tab="tracking">
            <span class="mailsuite-tab-icon">📈</span>
            <span>Tracking</span>
          </div>
          <div class="mailsuite-tab" data-tab="schedule">
            <span class="mailsuite-tab-icon">⏰</span>
            <span>Schedule</span>
          </div>
        </div>
        
        <div class="mailsuite-tab-content">
          <!-- Mail Merge Tab -->
          <div class="mailsuite-tab-pane active" data-tab="mailmerge">
            <div class="mailsuite-section">
              <div class="mailsuite-section-header">
                <h3>📋 Recipients</h3>
              </div>
              <div class="mailsuite-upload-area">
                <div class="mailsuite-upload-box">
                  <input type="file" id="mailsuite-csv-upload" accept=".csv,.xlsx" style="display: none;">
                  <div class="mailsuite-upload-content" onclick="document.getElementById('mailsuite-csv-upload').click()">
                    <div class="mailsuite-upload-icon">📁</div>
                    <div class="mailsuite-upload-text">
                      <strong>Click to upload CSV or Excel file</strong>
                      <br><small>Drag and drop files here</small>
                    </div>
                  </div>
                </div>
                <div class="mailsuite-upload-status">
                  <span id="mailsuite-recipient-count">No recipients loaded</span>
                </div>
              </div>
            </div>
            
            <div class="mailsuite-section">
              <div class="mailsuite-section-header">
                <h3>🏷️ Personalization</h3>
              </div>
              <div class="mailsuite-variables">
                <div class="mailsuite-variables-header">Available merge tags:</div>
                <div class="mailsuite-variables-list" id="mailsuite-variables-list">
                  <span class="mailsuite-variable" data-var="{{firstName}}">{{firstName}}</span>
                  <span class="mailsuite-variable" data-var="{{lastName}}">{{lastName}}</span>
                  <span class="mailsuite-variable" data-var="{{email}}">{{email}}</span>
                  <span class="mailsuite-variable" data-var="{{company}}">{{company}}</span>
                </div>
              </div>
            </div>
            
            <div class="mailsuite-section">
              <div class="mailsuite-section-header">
                <h3>⚙️ Sending Options</h3>
              </div>
              <div class="mailsuite-options">
                <label class="mailsuite-checkbox">
                  <input type="checkbox" id="mailsuite-track-opens" checked>
                  <span class="mailsuite-checkmark"></span>
                  Track email opens
                </label>
                <label class="mailsuite-checkbox">
                  <input type="checkbox" id="mailsuite-track-clicks" checked>
                  <span class="mailsuite-checkmark"></span>
                  Track link clicks
                </label>
                <label class="mailsuite-checkbox">
                  <input type="checkbox" id="mailsuite-use-subdomains">
                  <span class="mailsuite-checkmark"></span>
                  Use subdomain rotation
                </label>
              </div>
              
              <div class="mailsuite-subdomain-config" id="mailsuite-subdomain-config" style="display: none;">
                <div class="mailsuite-input-group">
                  <label>Base Domain:</label>
                  <input type="text" id="mailsuite-base-domain" placeholder="yourdomain.com">
                </div>
                <div class="mailsuite-input-group">
                  <label>Subdomains (one per line):</label>
                  <textarea id="mailsuite-subdomains" placeholder="mail1&#10;mail2&#10;mail3" rows="3"></textarea>
                </div>
                <div class="mailsuite-input-group">
                  <label>Sender Name:</label>
                  <input type="text" id="mailsuite-sender-name" placeholder="Your Name">
                </div>
              </div>
            </div>
            
            <div class="mailsuite-section">
              <div class="mailsuite-action-buttons">
                <button class="mailsuite-btn mailsuite-btn-primary" id="mailsuite-start-campaign">
                  <span class="mailsuite-btn-icon">🚀</span>
                  Start Mail Merge
                </button>
                <button class="mailsuite-btn mailsuite-btn-secondary" id="mailsuite-preview">
                  <span class="mailsuite-btn-icon">👁️</span>
                  Preview
                </button>
              </div>
            </div>
          </div>
          
          <!-- Templates Tab -->
          <div class="mailsuite-tab-pane" data-tab="templates">
            <div class="mailsuite-section">
              <div class="mailsuite-section-header">
                <h3>📝 Email Templates</h3>
              </div>
              <div class="mailsuite-templates-list" id="mailsuite-templates-list">
                <div class="mailsuite-no-templates">No templates saved yet</div>
              </div>
              <div class="mailsuite-template-actions">
                <input type="text" id="mailsuite-template-name" placeholder="Template name" class="mailsuite-input">
                <button class="mailsuite-btn mailsuite-btn-primary" id="mailsuite-save-template">
                  Save Current as Template
                </button>
              </div>
            </div>
          </div>
          
          <!-- Tracking Tab -->
          <div class="mailsuite-tab-pane" data-tab="tracking">
            <div class="mailsuite-section">
              <div class="mailsuite-section-header">
                <h3>📈 Email Analytics</h3>
              </div>
              <div class="mailsuite-stats-grid">
                <div class="mailsuite-stat-card">
                  <div class="mailsuite-stat-number" id="mailsuite-total-sent">0</div>
                  <div class="mailsuite-stat-label">Total Sent</div>
                </div>
                <div class="mailsuite-stat-card">
                  <div class="mailsuite-stat-number" id="mailsuite-total-opens">0</div>
                  <div class="mailsuite-stat-label">Opens</div>
                </div>
                <div class="mailsuite-stat-card">
                  <div class="mailsuite-stat-number" id="mailsuite-open-rate">0%</div>
                  <div class="mailsuite-stat-label">Open Rate</div>
                </div>
                <div class="mailsuite-stat-card">
                  <div class="mailsuite-stat-number" id="mailsuite-click-rate">0%</div>
                  <div class="mailsuite-stat-label">Click Rate</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Schedule Tab -->
          <div class="mailsuite-tab-pane" data-tab="schedule">
            <div class="mailsuite-section">
              <div class="mailsuite-section-header">
                <h3>⏰ Schedule Sending</h3>
              </div>
              <label class="mailsuite-checkbox">
                <input type="checkbox" id="mailsuite-schedule-enabled">
                <span class="mailsuite-checkmark"></span>
                Schedule this campaign
              </label>
              
              <div class="mailsuite-schedule-options" id="mailsuite-schedule-options" style="display: none;">
                <div class="mailsuite-input-group">
                  <label>Send Date & Time:</label>
                  <input type="datetime-local" id="mailsuite-schedule-datetime" class="mailsuite-input">
                </div>
                <div class="mailsuite-input-group">
                  <label>Time Zone:</label>
                  <select id="mailsuite-timezone" class="mailsuite-input">
                    <option value="UTC">UTC</option>
                    <option value="America/New_York">Eastern Time</option>
                    <option value="America/Chicago">Central Time</option>
                    <option value="America/Denver">Mountain Time</option>
                    <option value="America/Los_Angeles">Pacific Time</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // Insert panel into compose window
    const composeBody = composeWindow.querySelector('.Am.Al.editable');
    if (composeBody) {
      composeBody.parentNode.insertBefore(panel, composeBody);
      this.setupPanelEventListeners(panel, composeWindow);
    }
  }

  setupPanelEventListeners(panel, composeWindow) {
    // Panel controls
    panel.querySelector('.mailsuite-close').addEventListener('click', () => {
      panel.style.display = 'none';
    });

    panel.querySelector('.mailsuite-minimize').addEventListener('click', () => {
      const body = panel.querySelector('.mailsuite-panel-body');
      body.style.display = body.style.display === 'none' ? 'block' : 'none';
    });

    // Tab switching
    panel.querySelectorAll('.mailsuite-tab').forEach(tab => {
      tab.addEventListener('click', () => {
        // Remove active class from all tabs and panes
        panel.querySelectorAll('.mailsuite-tab').forEach(t => t.classList.remove('active'));
        panel.querySelectorAll('.mailsuite-tab-pane').forEach(p => p.classList.remove('active'));
        
        // Add active class to clicked tab and corresponding pane
        tab.classList.add('active');
        const targetTab = tab.dataset.tab;
        panel.querySelector(`[data-tab="${targetTab}"].mailsuite-tab-pane`).classList.add('active');
      });
    });

    // CSV upload
    const csvUpload = panel.querySelector('#mailsuite-csv-upload');
    csvUpload.addEventListener('change', (e) => this.handleCsvUpload(e, panel));

    // Variable insertion
    panel.querySelectorAll('.mailsuite-variable').forEach(variable => {
      variable.addEventListener('click', () => {
        this.insertVariable(variable.dataset.var, composeWindow);
      });
    });

    // Subdomain toggle
    const subdomainCheckbox = panel.querySelector('#mailsuite-use-subdomains');
    const subdomainConfig = panel.querySelector('#mailsuite-subdomain-config');
    subdomainCheckbox.addEventListener('change', () => {
      subdomainConfig.style.display = subdomainCheckbox.checked ? 'block' : 'none';
    });

    // Schedule toggle
    const scheduleCheckbox = panel.querySelector('#mailsuite-schedule-enabled');
    const scheduleOptions = panel.querySelector('#mailsuite-schedule-options');
    scheduleCheckbox.addEventListener('change', () => {
      scheduleOptions.style.display = scheduleCheckbox.checked ? 'block' : 'none';
    });

    // Start campaign
    panel.querySelector('#mailsuite-start-campaign').addEventListener('click', () => {
      this.startCampaign(panel, composeWindow);
    });

    // Save template
    panel.querySelector('#mailsuite-save-template').addEventListener('click', () => {
      this.saveTemplate(panel, composeWindow);
    });

    // Preview
    panel.querySelector('#mailsuite-preview').addEventListener('click', () => {
      this.previewEmail(panel, composeWindow);
    });
  }

  handleCsvUpload(event, panel) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const contacts = this.parseCSV(e.target.result);
        this.contacts = contacts;
        
        // Update UI
        panel.querySelector('#mailsuite-recipient-count').textContent = 
          `${contacts.length} recipients loaded`;
        
        // Update variables
        this.updateVariables(contacts, panel);
        
        // Store contacts
        chrome.storage.local.set({ mailsuiteContacts: contacts });
        
      } catch (error) {
        panel.querySelector('#mailsuite-recipient-count').textContent = 
          `Error: ${error.message}`;
      }
    };
    
    reader.readAsText(file);
  }

  parseCSV(csvContent) {
    const lines = csvContent.split('\n');
    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    
    const contacts = [];
    for (let i = 1; i < lines.length; i++) {
      if (!lines[i].trim()) continue;
      
      const values = lines[i].split(',').map(v => v.trim());
      const contact = {};
      
      headers.forEach((header, index) => {
        if (values[index]) {
          contact[header] = values[index];
        }
      });
      
      if (contact.email || contact['email address']) {
        contacts.push(contact);
      }
    }
    
    return contacts;
  }

  updateVariables(contacts, panel) {
    if (contacts.length === 0) return;
    
    const variablesList = panel.querySelector('#mailsuite-variables-list');
    const headers = Object.keys(contacts[0]);
    
    variablesList.innerHTML = '';
    
    headers.forEach(header => {
      const variable = document.createElement('span');
      variable.className = 'mailsuite-variable';
      variable.dataset.var = `{{${header}}}`;
      variable.textContent = `{{${header}}}`;
      variable.addEventListener('click', () => {
        this.insertVariable(variable.dataset.var, panel.closest('.M9'));
      });
      variablesList.appendChild(variable);
    });
  }

  insertVariable(variable, composeWindow) {
    const editor = composeWindow.querySelector('.Am.Al.editable');
    if (!editor) return;
    
    // Insert at cursor position
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const textNode = document.createTextNode(variable);
      range.insertNode(textNode);
      range.setStartAfter(textNode);
      range.setEndAfter(textNode);
      selection.removeAllRanges();
      selection.addRange(range);
    } else {
      editor.innerHTML += variable;
    }
    
    editor.dispatchEvent(new Event('input', { bubbles: true }));
  }

  startCampaign(panel, composeWindow) {
    if (!this.contacts || this.contacts.length === 0) {
      alert('Please upload a CSV file with recipients first.');
      return;
    }

    // Get email content
    const subject = composeWindow.querySelector('input[name="subjectbox"]').value;
    const body = composeWindow.querySelector('.Am.Al.editable').innerHTML;
    
    if (!subject || !body) {
      alert('Please enter both subject and message content.');
      return;
    }

    // Get settings
    const trackOpens = panel.querySelector('#mailsuite-track-opens').checked;
    const trackClicks = panel.querySelector('#mailsuite-track-clicks').checked;
    const useSubdomains = panel.querySelector('#mailsuite-use-subdomains').checked;
    const isScheduled = panel.querySelector('#mailsuite-schedule-enabled').checked;

    // Create campaign
    const campaign = {
      id: Date.now().toString(),
      subject,
      body,
      contacts: this.contacts,
      settings: {
        trackOpens,
        trackClicks,
        useSubdomains,
        isScheduled
      },
      status: 'pending',
      created: new Date().toISOString()
    };

    if (useSubdomains) {
      campaign.subdomains = {
        baseDomain: panel.querySelector('#mailsuite-base-domain').value,
        list: panel.querySelector('#mailsuite-subdomains').value.split('\n').filter(s => s.trim()),
        senderName: panel.querySelector('#mailsuite-sender-name').value
      };
    }

    if (isScheduled) {
      campaign.scheduledFor = panel.querySelector('#mailsuite-schedule-datetime').value;
      campaign.timezone = panel.querySelector('#mailsuite-timezone').value;
    }

    // Store and start campaign
    chrome.storage.local.set({ 
      mailsuiteCampaign: campaign,
      campaignActive: true 
    }, () => {
      if (isScheduled) {
        alert(`Campaign scheduled for ${campaign.scheduledFor}`);
      } else {
        alert(`Starting mail merge for ${this.contacts.length} recipients...`);
        chrome.runtime.sendMessage({ 
          type: 'startCampaign', 
          campaign 
        });
      }
      
      // Hide panel
      panel.style.display = 'none';
    });
  }

  saveTemplate(panel, composeWindow) {
    const templateName = panel.querySelector('#mailsuite-template-name').value;
    if (!templateName) {
      alert('Please enter a template name.');
      return;
    }

    const subject = composeWindow.querySelector('input[name="subjectbox"]').value;
    const body = composeWindow.querySelector('.Am.Al.editable').innerHTML;

    const template = {
      id: Date.now().toString(),
      name: templateName,
      subject,
      body,
      created: new Date().toISOString()
    };

    chrome.storage.local.get(['mailsuiteTemplates'], (data) => {
      const templates = data.mailsuiteTemplates || [];
      templates.push(template);
      
      chrome.storage.local.set({ mailsuiteTemplates: templates }, () => {
        alert('Template saved successfully!');
        panel.querySelector('#mailsuite-template-name').value = '';
        this.loadTemplates(panel);
      });
    });
  }

  previewEmail(panel, composeWindow) {
    if (!this.contacts || this.contacts.length === 0) {
      alert('Please upload recipients first to see preview.');
      return;
    }

    const subject = composeWindow.querySelector('input[name="subjectbox"]').value;
    const body = composeWindow.querySelector('.Am.Al.editable').innerHTML;
    
    // Use first contact for preview
    const contact = this.contacts[0];
    const previewSubject = this.personalizeText(subject, contact);
    const previewBody = this.personalizeText(body, contact);

    // Create preview modal
    const modal = document.createElement('div');
    modal.className = 'mailsuite-preview-modal';
    modal.innerHTML = `
      <div class="mailsuite-preview-content">
        <div class="mailsuite-preview-header">
          <h3>Email Preview</h3>
          <button class="mailsuite-preview-close">×</button>
        </div>
        <div class="mailsuite-preview-body">
          <div class="mailsuite-preview-field">
            <strong>To:</strong> ${contact.email}
          </div>
          <div class="mailsuite-preview-field">
            <strong>Subject:</strong> ${previewSubject}
          </div>
          <div class="mailsuite-preview-field">
            <strong>Message:</strong>
            <div class="mailsuite-preview-message">${previewBody}</div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    modal.querySelector('.mailsuite-preview-close').addEventListener('click', () => {
      modal.remove();
    });

    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }

  personalizeText(text, contact) {
    let personalized = text;
    
    Object.keys(contact).forEach(key => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      personalized = personalized.replace(regex, contact[key] || '');
    });
    
    return personalized;
  }

  openMailSuiteDashboard() {
    // This would open the main MailSuite dashboard
    chrome.action.openPopup();
  }

  setupGlobalListeners() {
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'campaignProgress') {
        this.updateProgress(message.data);
      }
    });
  }

  updateProgress(data) {
    // Update any visible progress indicators
    const panels = document.querySelectorAll('.mailsuite-panel');
    panels.forEach(panel => {
      const sentStat = panel.querySelector('#mailsuite-total-sent');
      if (sentStat) {
        sentStat.textContent = data.sent || 0;
      }
    });
  }
}

// Initialize MailSuite when Gmail loads
if (window.location.hostname === 'mail.google.com') {
  new AuthenticMailSuite();
}
