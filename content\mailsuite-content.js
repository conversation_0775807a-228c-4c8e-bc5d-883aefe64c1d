// MailSuite Clone - Authentic MailSuite Experience
class MailSuiteGmail {
  constructor() {
    this.isInitialized = false;
    this.currentCampaign = null;
    this.templates = [];
    this.contacts = [];
    this.trackingDomain = 'your-tracking-domain.com';
    this.mailsuitePanel = null;

    this.init();
  }

  init() {
    console.log('MailSuite Clone: Initializing...');

    // Wait for Gmail to load
    this.waitForGmail(() => {
      this.injectMailSuiteInterface();
      this.setupEventListeners();
      this.loadUserData();
      this.checkForActiveTab();
      this.isInitialized = true;
    });
  }

  waitForGmail(callback) {
    const checkGmail = () => {
      if (document.querySelector('.nH') && document.querySelector('.G-tF')) {
        callback();
      } else {
        setTimeout(checkGmail, 1000);
      }
    };
    checkGmail();
  }

  injectMailSuiteInterface() {
    // Add MailSuite button to Gmail toolbar
    this.addToolbarButton();

    // Monitor for compose windows
    this.observeComposeWindows();
  }

  addToolbarButton() {
    const toolbar = document.querySelector('.G-tF');
    if (!toolbar || document.getElementById('mailsuite-btn')) return;

    const button = document.createElement('div');
    button.id = 'mailsuite-btn';
    button.className = 'T-I J-J5-Ji T-I-KE L3 mailsuite-toolbar-btn';
    button.innerHTML = `
      <div class="asa">
        <span class="mailsuite-btn-text">MailSuite</span>
      </div>
    `;

    button.addEventListener('click', () => this.openMailSuiteDashboard());
    toolbar.appendChild(button);
  }

  observeComposeWindows() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1 && node.querySelector && node.querySelector('.M9')) {
            // New compose window detected
            setTimeout(() => this.enhanceComposeWindow(node), 1000);
          }
        });
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });
  }

  checkForActiveTab() {
    // Check if this tab should be sending emails
    const tabId = window.location.href.includes('compose=new') ? 'compose_tab' : 'normal_tab';

    chrome.storage.local.get([`tab_${tabId}_contact`, `tab_${tabId}_campaign`], (data) => {
      const contact = data[`tab_${tabId}_contact`];
      const campaignId = data[`tab_${tabId}_campaign`];

      if (contact && campaignId) {
        // This tab should send an email
        this.sendEmailForTab(contact, campaignId);
      }
    });
  }

  async sendEmailForTab(contact, campaignId) {
    // Get campaign configuration
    const result = await chrome.storage.local.get(['campaigns']);
    const campaigns = result.campaigns || {};
    const campaign = campaigns[campaignId];

    if (!campaign) return;

    // Wait for compose window to load
    this.waitForElement('div[role="textbox"][aria-label="Message Body"]', (composeBody) => {
      this.fillAndSendEmail(contact, campaign, composeBody);
    });
  }

  async fillAndSendEmail(contact, campaign, composeBody) {
    try {
      // Fill recipient
      const toField = document.querySelector('input[name="to"]');
      if (toField) {
        toField.value = contact.email;
        toField.dispatchEvent(new Event('input', { bubbles: true }));
      }

      // Set from address if using subdomains
      if (campaign.settings.useSubdomains && campaign.subdomains) {
        const fromEmail = this.getNextFromAddress(campaign);
        await this.setFromAddress(fromEmail);
      }

      // Fill subject
      const subjectField = document.querySelector('input[name="subjectbox"]');
      if (subjectField) {
        const personalizedSubject = this.personalizeText(campaign.subject, contact);
        subjectField.value = personalizedSubject;
        subjectField.dispatchEvent(new Event('input', { bubbles: true }));
      }

      // Fill and track body
      let personalizedBody = this.personalizeText(campaign.body, contact);

      if (campaign.settings.trackOpens || campaign.settings.trackClicks) {
        personalizedBody = this.addEmailTracking(personalizedBody, campaign.id, contact.email, campaign.settings);
      }

      composeBody.innerHTML = personalizedBody;
      composeBody.dispatchEvent(new Event('input', { bubbles: true }));

      // Wait and send
      setTimeout(() => {
        const sendButton = document.querySelector('div[role="button"][data-tooltip^="Send"]');
        if (sendButton) {
          sendButton.click();

          // Update campaign progress
          chrome.runtime.sendMessage({
            type: 'updateProgress',
            campaignId: campaign.id,
            progress: { sent: 1 }
          });

          // Close tab after sending
          setTimeout(() => {
            window.close();
          }, 2000);
        }
      }, 1000);

    } catch (error) {
      console.error('Error sending email:', error);
    }
  }

  getNextFromAddress(campaign) {
    const subdomains = campaign.subdomains.list;
    const currentIndex = campaign.subdomains.currentIndex || 0;

    let subdomainIndex;
    if (campaign.subdomains.mode === 'rotate') {
      subdomainIndex = currentIndex % subdomains.length;
      campaign.subdomains.currentIndex = currentIndex + 1;
    } else {
      subdomainIndex = Math.floor(Math.random() * subdomains.length);
    }

    const subdomain = subdomains[subdomainIndex];
    return `${campaign.subdomains.senderName} <${subdomain}@${campaign.subdomains.baseDomain}>`;
  }

  async setFromAddress(fromEmail) {
    return new Promise((resolve) => {
      this.waitForElement('div[role="button"][data-tooltip^="From"]', (fromButton) => {
        fromButton.click();

        this.waitForElement('div[role="menuitem"]:last-child', (editFromOption) => {
          editFromOption.click();

          this.waitForElement('input[aria-label="From"]', (fromField) => {
            fromField.value = fromEmail;
            fromField.dispatchEvent(new Event('input', { bubbles: true }));

            setTimeout(resolve, 500);
          });
        });
      }, 10, 500);
    });
  }

  personalizeText(text, contact) {
    let personalized = text;

    Object.keys(contact).forEach(key => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      personalized = personalized.replace(regex, contact[key] || '');
    });

    return personalized;
  }

  addEmailTracking(emailBody, campaignId, recipientEmail, settings) {
    let trackedBody = emailBody;
    const recipientId = btoa(recipientEmail).replace(/[^a-zA-Z0-9]/g, '');

    if (settings.trackOpens) {
      const trackingPixel = `<img src="https://${this.trackingDomain}/track/open/${campaignId}/${recipientId}" width="1" height="1" style="display:none;">`;
      trackedBody += trackingPixel;
    }

    if (settings.trackClicks) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(trackedBody, 'text/html');
      const links = doc.querySelectorAll('a[href]');

      links.forEach(link => {
        const originalUrl = link.getAttribute('href');
        if (originalUrl && !originalUrl.startsWith('mailto:')) {
          const trackingUrl = `https://${this.trackingDomain}/track/click/${campaignId}/${recipientId}?url=${encodeURIComponent(originalUrl)}`;
          link.setAttribute('href', trackingUrl);
        }
      });

      trackedBody = doc.body.innerHTML;
    }

    if (settings.addUnsubscribe) {
      const unsubscribeLink = `
        <div style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #eee; font-size: 12px; color: #777; text-align: center;">
          <a href="https://${this.trackingDomain}/unsubscribe/${campaignId}/${recipientId}" style="color: #777;">Unsubscribe</a>
        </div>
      `;
      trackedBody += unsubscribeLink;
    }

    return trackedBody;
  }

  waitForElement(selector, callback, maxAttempts = 50, interval = 200) {
    let attempts = 0;

    const checkElement = () => {
      const element = document.querySelector(selector);
      if (element) {
        callback(element);
        return;
      }

      attempts++;
      if (attempts < maxAttempts) {
        setTimeout(checkElement, interval);
      }
    };

    checkElement();
  }

  enhanceComposeWindow(composeWindow) {
    if (composeWindow.querySelector('.mailsuite-enhanced')) return;

    composeWindow.classList.add('mailsuite-enhanced');
    this.addComposePanel(composeWindow);
  }

  addComposePanel(composeWindow) {
    const composeBody = composeWindow.querySelector('.Am.Al.editable');
    if (!composeBody) return;

    const panel = document.createElement('div');
    panel.className = 'mailsuite-compose-panel';
    panel.innerHTML = `
      <div class="mailsuite-panel-header">
        <h3>MailSuite</h3>
        <button class="mailsuite-toggle-btn" onclick="this.parentElement.parentElement.classList.toggle('collapsed')">−</button>
      </div>

      <div class="mailsuite-panel-content">
        <div class="mailsuite-tabs">
          <button class="mailsuite-tab active" data-tab="merge">Mail Merge</button>
          <button class="mailsuite-tab" data-tab="templates">Templates</button>
          <button class="mailsuite-tab" data-tab="tracking">Tracking</button>
        </div>

        <!-- Mail Merge Tab -->
        <div class="mailsuite-tab-content active" data-tab="merge">
          <div class="mailsuite-section">
            <label>Upload Contacts (CSV):</label>
            <input type="file" class="mailsuite-csv-upload" accept=".csv">
            <div class="mailsuite-contact-count">No contacts loaded</div>
          </div>

          <div class="mailsuite-section">
            <label>Variables:</label>
            <div class="mailsuite-variables">
              <span class="mailsuite-var" data-var="{{firstName}}">First Name</span>
              <span class="mailsuite-var" data-var="{{lastName}}">Last Name</span>
              <span class="mailsuite-var" data-var="{{email}}">Email</span>
              <span class="mailsuite-var" data-var="{{company}}">Company</span>
            </div>
          </div>

          <div class="mailsuite-section">
            <label>Sending Options:</label>
            <div class="mailsuite-field">
              <label>
                <input type="checkbox" class="mailsuite-subdomain-rotation">
                Use subdomain rotation
              </label>
            </div>
            <div class="mailsuite-subdomain-settings" style="display: none;">
              <input type="text" placeholder="Base domain (e.g., example.com)" class="mailsuite-base-domain">
              <textarea placeholder="Subdomains (one per line)" class="mailsuite-subdomains"></textarea>
              <input type="text" placeholder="Sender name" class="mailsuite-sender-name">
            </div>
          </div>

          <button class="mailsuite-btn-primary mailsuite-start-campaign">Start Mail Merge</button>
        </div>

        <!-- Templates Tab -->
        <div class="mailsuite-tab-content" data-tab="templates">
          <div class="mailsuite-section">
            <label>Email Templates:</label>
            <select class="mailsuite-template-select">
              <option value="">Select a template...</option>
            </select>
            <button class="mailsuite-btn-secondary mailsuite-save-template">Save Current as Template</button>
          </div>

          <div class="mailsuite-section">
            <label>Template Name:</label>
            <input type="text" class="mailsuite-template-name" placeholder="Enter template name">
          </div>
        </div>

        <!-- Tracking Tab -->
        <div class="mailsuite-tab-content" data-tab="tracking">
          <div class="mailsuite-section">
            <label>
              <input type="checkbox" class="mailsuite-track-opens" checked>
              Track email opens
            </label>
          </div>

          <div class="mailsuite-section">
            <label>
              <input type="checkbox" class="mailsuite-track-clicks" checked>
              Track link clicks
            </label>
          </div>

          <div class="mailsuite-section">
            <label>
              <input type="checkbox" class="mailsuite-auto-unsubscribe" checked>
              Add unsubscribe link
            </label>
          </div>

          <button class="mailsuite-btn-secondary mailsuite-view-analytics">View Analytics</button>
        </div>
      </div>
    `;

    // Insert panel before compose form
    const composeForm = composeWindow.querySelector('form');
    if (composeForm) {
      composeForm.parentNode.insertBefore(panel, composeForm);
      this.setupPanelEventListeners(panel, composeWindow);
    }
  }

  setupPanelEventListeners(panel, composeWindow) {
    // Tab switching
    panel.querySelectorAll('.mailsuite-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        const targetTab = e.target.dataset.tab;

        panel.querySelectorAll('.mailsuite-tab').forEach(t => t.classList.remove('active'));
        panel.querySelectorAll('.mailsuite-tab-content').forEach(c => c.classList.remove('active'));

        e.target.classList.add('active');
        panel.querySelector(`[data-tab="${targetTab}"].mailsuite-tab-content`).classList.add('active');
      });
    });

    // CSV upload
    const csvUpload = panel.querySelector('.mailsuite-csv-upload');
    csvUpload.addEventListener('change', (e) => this.handleCsvUpload(e, panel));

    // Variable insertion
    panel.querySelectorAll('.mailsuite-var').forEach(varBtn => {
      varBtn.addEventListener('click', (e) => {
        const variable = e.target.dataset.var;
        this.insertVariableIntoCompose(variable, composeWindow);
      });
    });

    // Subdomain rotation toggle
    const subdomainToggle = panel.querySelector('.mailsuite-subdomain-rotation');
    const subdomainSettings = panel.querySelector('.mailsuite-subdomain-settings');
    subdomainToggle.addEventListener('change', (e) => {
      subdomainSettings.style.display = e.target.checked ? 'block' : 'none';
    });

    // Start campaign
    const startBtn = panel.querySelector('.mailsuite-start-campaign');
    startBtn.addEventListener('click', () => this.startMailMergeCampaign(panel, composeWindow));

    // Save template
    const saveTemplateBtn = panel.querySelector('.mailsuite-save-template');
    saveTemplateBtn.addEventListener('click', () => this.saveTemplate(panel, composeWindow));

    // Load template
    const templateSelect = panel.querySelector('.mailsuite-template-select');
    templateSelect.addEventListener('change', (e) => this.loadTemplate(e.target.value, composeWindow));
  }

  handleCsvUpload(event, panel) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const contacts = this.parseCSV(e.target.result);
        this.contacts = contacts;

        panel.querySelector('.mailsuite-contact-count').textContent = `${contacts.length} contacts loaded`;
        this.updateAvailableVariables(contacts, panel);

        chrome.storage.local.set({ mailsuiteContacts: contacts });

      } catch (error) {
        console.error('Error parsing CSV:', error);
        panel.querySelector('.mailsuite-contact-count').textContent = `Error: ${error.message}`;
      }
    };

    reader.readAsText(file);
  }

  parseCSV(csvContent) {
    const lines = csvContent.split('\n');
    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());

    const contacts = [];
    for (let i = 1; i < lines.length; i++) {
      if (!lines[i].trim()) continue;

      const values = lines[i].split(',').map(v => v.trim());
      const contact = {};

      headers.forEach((header, index) => {
        if (values[index]) {
          contact[header] = values[index];
        }
      });

      contacts.push(contact);
    }

    return contacts;
  }

  updateAvailableVariables(contacts, panel) {
    if (contacts.length === 0) return;

    const variablesContainer = panel.querySelector('.mailsuite-variables');
    const headers = Object.keys(contacts[0]);

    variablesContainer.innerHTML = '';

    headers.forEach(header => {
      const varBtn = document.createElement('span');
      varBtn.className = 'mailsuite-var';
      varBtn.dataset.var = `{{${header}}}`;
      varBtn.textContent = header.charAt(0).toUpperCase() + header.slice(1);
      varBtn.addEventListener('click', (e) => {
        this.insertVariableIntoCompose(e.target.dataset.var, panel.closest('.mailsuite-enhanced'));
      });
      variablesContainer.appendChild(varBtn);
    });
  }

  insertVariableIntoCompose(variable, composeWindow) {
    const composeBody = composeWindow.querySelector('.Am.Al.editable');
    if (!composeBody) return;

    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const textNode = document.createTextNode(variable);
      range.insertNode(textNode);
      range.setStartAfter(textNode);
      range.setEndAfter(textNode);
      selection.removeAllRanges();
      selection.addRange(range);
    } else {
      composeBody.innerHTML += variable;
    }

    composeBody.dispatchEvent(new Event('input', { bubbles: true }));
  }

  startMailMergeCampaign(panel, composeWindow) {
    if (!this.contacts || this.contacts.length === 0) {
      alert('Please upload a CSV file with contacts first.');
      return;
    }

    const subject = composeWindow.querySelector('input[name="subjectbox"]').value;
    const body = composeWindow.querySelector('.Am.Al.editable').innerHTML;

    if (!subject || !body) {
      alert('Please enter both subject and message content.');
      return;
    }

    const useSubdomains = panel.querySelector('.mailsuite-subdomain-rotation').checked;
    const trackOpens = panel.querySelector('.mailsuite-track-opens').checked;
    const trackClicks = panel.querySelector('.mailsuite-track-clicks').checked;
    const addUnsubscribe = panel.querySelector('.mailsuite-auto-unsubscribe').checked;

    const campaign = {
      id: Date.now().toString(),
      subject,
      body,
      contacts: this.contacts,
      settings: {
        useSubdomains,
        trackOpens,
        trackClicks,
        addUnsubscribe
      },
      status: 'pending',
      created: new Date().toISOString()
    };

    if (useSubdomains) {
      campaign.subdomains = {
        baseDomain: panel.querySelector('.mailsuite-base-domain').value,
        list: panel.querySelector('.mailsuite-subdomains').value.split('\n').filter(s => s.trim()),
        senderName: panel.querySelector('.mailsuite-sender-name').value,
        currentIndex: 0
      };
    }

    chrome.storage.local.set({
      mailsuiteCampaign: campaign,
      campaignActive: true
    }, () => {
      alert(`Starting mail merge campaign for ${this.contacts.length} contacts...`);
      chrome.runtime.sendMessage({
        type: 'startCampaign',
        campaign
      });

      composeWindow.querySelector('.Ha').click();
    });
  }

  setupEventListeners() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'campaignProgress') {
        this.updateCampaignProgress(message.data);
      }
    });
  }

  loadUserData() {
    chrome.storage.local.get(['mailsuiteTemplates', 'mailsuiteContacts'], (data) => {
      this.templates = data.mailsuiteTemplates || [];
      this.contacts = data.mailsuiteContacts || [];
    });
  }

  openMailSuiteDashboard() {
    // This will be handled by the popup
    chrome.action.openPopup();
  }
}

// Initialize MailSuite when page loads
if (window.location.hostname === 'mail.google.com') {
  new MailSuiteGmail();
}
