#!/usr/bin/env python3
"""
IT Depositors Marco <PERSON> - 500 Rows Each
Specifically for: IT_DEPOSITORS_MARCO 9.9k.xlsx
Keeps only First Name (renamed to Name), Email, Phone columns
Splits into 500-row files
"""

import pandas as pd
import os
import math
from pathlib import Path
import glob

def find_marco_file():
    """Find the IT_DEPOSITORS_MARCO file with various possible extensions"""
    possible_names = [
        "IT_DEPOSITORS_MARCO 9.9k.xlsx",
        "IT_DEPOSITORS_MARCO 9.9k.xls",
        "IT_DEPOSITORS_MARCO 9.9k.xlsm",
        "IT_DEPOSITORS_MARCO_9.9k.xlsx",
        "IT_DEPOSITORS_MARCO_9.9k.xls",
        "IT DEPOSITORS MARCO 9.9k.xlsx",
        "IT DEPOSITORS MARCO 9.9k.xls"
    ]
    
    for name in possible_names:
        if os.path.exists(name):
            return name
    
    # Search for files containing "MA<PERSON><PERSON>" and "DEPOSITORS"
    all_files = glob.glob("*.xlsx") + glob.glob("*.xls") + glob.glob("*.xlsm")
    for file in all_files:
        if "marco" in file.lower() and "depositor" in file.lower():
            return file
    
    return None

def main():
    print("🚀 IT Depositors Marco Splitter - 500 Rows Each")
    print("=" * 60)
    
    # Find the file
    excel_file = find_marco_file()
    rows_per_file = 500
    
    if not excel_file:
        print(f"❌ IT_DEPOSITORS_MARCO file not found!")
        print(f"\n📁 Current directory: {os.getcwd()}")
        print(f"📋 Excel files in current directory:")
        
        excel_files = glob.glob("*.xlsx") + glob.glob("*.xls") + glob.glob("*.xlsm")
        if excel_files:
            for file in excel_files:
                size = os.path.getsize(file) / 1024 / 1024  # MB
                print(f"   📄 {file} ({size:.1f} MB)")
        else:
            print("   (No Excel files found)")
        
        print(f"\n💡 Looking for files like:")
        print(f"   • IT_DEPOSITORS_MARCO 9.9k.xlsx")
        print(f"   • IT DEPOSITORS MARCO 9.9k.xlsx")
        print(f"   • Any file containing 'MARCO' and 'DEPOSITOR'")
        input("\nPress Enter to exit...")
        return
    
    print(f"✅ Found file: {excel_file}")
    
    try:
        # Read Excel file
        print(f"📖 Reading Excel file...")
        df = pd.read_excel(excel_file)
        
        print(f"✅ File loaded successfully!")
        print(f"📊 Total rows: {len(df):,}")
        print(f"📊 Total columns: {len(df.columns)}")
        
        # Show all columns
        print(f"\n📋 Available columns:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        # Show sample data
        print(f"\n📖 First 3 rows preview:")
        print(df.head(3).to_string())
        
        # Auto-detect columns
        first_name_col = None
        email_col = None
        phone_col = None
        
        for col in df.columns:
            col_lower = col.lower().strip()
            
            # Look for First Name column
            if not first_name_col:
                if ('first' in col_lower and 'name' in col_lower) or col_lower == 'firstname':
                    first_name_col = col
                elif col_lower in ['name', 'nome', 'first name']:
                    first_name_col = col
                elif 'nome' in col_lower and 'first' not in col_lower:
                    first_name_col = col
            
            # Look for Email column
            if not email_col:
                if 'email' in col_lower or 'mail' in col_lower or 'e-mail' in col_lower:
                    email_col = col
            
            # Look for Phone column
            if not phone_col:
                if any(word in col_lower for word in ['phone', 'tel', 'mobile', 'cell', 'telefone', 'cellulare']):
                    phone_col = col
        
        print(f"\n🔍 Auto-detected columns:")
        print(f"   First Name: {first_name_col if first_name_col else 'NOT FOUND'}")
        print(f"   Email: {email_col if email_col else 'NOT FOUND'}")
        print(f"   Phone: {phone_col if phone_col else 'NOT FOUND'}")
        
        # Manual selection if auto-detection failed
        if not first_name_col:
            print(f"\n❓ Which column contains FIRST NAME or NAME?")
            for i, col in enumerate(df.columns, 1):
                print(f"   {i}. {col}")
            while True:
                try:
                    choice = input("Select First Name column number: ")
                    index = int(choice) - 1
                    if 0 <= index < len(df.columns):
                        first_name_col = df.columns[index]
                        break
                    else:
                        print("❌ Invalid choice!")
                except ValueError:
                    print("❌ Please enter a number!")
        
        if not email_col:
            print(f"\n❓ Which column contains EMAIL?")
            for i, col in enumerate(df.columns, 1):
                print(f"   {i}. {col}")
            while True:
                try:
                    choice = input("Select Email column number: ")
                    index = int(choice) - 1
                    if 0 <= index < len(df.columns):
                        email_col = df.columns[index]
                        break
                    else:
                        print("❌ Invalid choice!")
                except ValueError:
                    print("❌ Please enter a number!")
        
        if not phone_col:
            print(f"\n❓ Which column contains PHONE?")
            for i, col in enumerate(df.columns, 1):
                print(f"   {i}. {col}")
            print(f"   0. Skip phone column (Name + Email only)")
            while True:
                try:
                    choice = input("Select Phone column number (or 0 to skip): ")
                    if choice == "0":
                        phone_col = None
                        break
                    index = int(choice) - 1
                    if 0 <= index < len(df.columns):
                        phone_col = df.columns[index]
                        break
                    else:
                        print("❌ Invalid choice!")
                except ValueError:
                    print("❌ Please enter a number!")
        
        # Create filtered dataframe
        print(f"\n🧹 Processing data...")
        
        # Select columns and rename
        if phone_col:
            df_filtered = df[[first_name_col, email_col, phone_col]].copy()
            df_filtered.columns = ['Name', 'Email', 'Phone']
        else:
            df_filtered = df[[first_name_col, email_col]].copy()
            df_filtered.columns = ['Name', 'Email']
            df_filtered['Phone'] = ''  # Add empty phone column
        
        original_rows = len(df_filtered)
        
        # Clean data
        print(f"🧹 Cleaning data...")
        
        # Remove rows without email
        df_filtered = df_filtered.dropna(subset=['Email'])
        
        # Remove invalid emails (must contain @)
        df_filtered = df_filtered[df_filtered['Email'].str.contains('@', na=False)]
        
        # Remove rows where email is just whitespace
        df_filtered = df_filtered[df_filtered['Email'].str.strip() != '']
        
        # Fill empty names and phones with empty string
        df_filtered['Name'] = df_filtered['Name'].fillna('')
        df_filtered['Phone'] = df_filtered['Phone'].fillna('')
        
        # Remove completely empty rows
        df_filtered = df_filtered.dropna(how='all')
        
        final_rows = len(df_filtered)
        removed_rows = original_rows - final_rows
        
        print(f"✅ Data processed:")
        print(f"   Original rows: {original_rows:,}")
        print(f"   Final rows: {final_rows:,}")
        print(f"   Removed rows: {removed_rows:,}")
        
        if final_rows == 0:
            print("❌ No valid data found!")
            input("Press Enter to exit...")
            return
        
        # Show sample of cleaned data
        print(f"\n📋 Sample cleaned data:")
        print(df_filtered.head(3).to_string(index=False))
        
        # Calculate number of files
        total_files = math.ceil(final_rows / rows_per_file)
        
        # Create output folder
        output_folder = "it_depositors_marco_csv_500"
        Path(output_folder).mkdir(exist_ok=True)
        
        print(f"\n📁 Creating {total_files} CSV files with {rows_per_file} rows each...")
        print(f"📁 Output folder: {output_folder}/")
        
        # Split and save files
        for i in range(total_files):
            start_row = i * rows_per_file
            end_row = min((i + 1) * rows_per_file, final_rows)
            
            chunk = df_filtered.iloc[start_row:end_row]
            
            filename = f"it_depositors_marco_part_{i+1:03d}_of_{total_files:03d}.csv"
            filepath = os.path.join(output_folder, filename)
            
            # Save to CSV
            chunk.to_csv(filepath, index=False)
            
            print(f"✅ {filename} ({len(chunk)} rows)")
        
        # Create summary file
        summary_file = os.path.join(output_folder, "SUMMARY.txt")
        with open(summary_file, 'w') as f:
            f.write("IT Depositors Marco Email Campaign Files - 500 Rows Each\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Source file: {excel_file}\n")
            f.write(f"Total contacts: {final_rows:,}\n")
            f.write(f"Files created: {total_files}\n")
            f.write(f"Rows per file: {rows_per_file}\n")
            f.write(f"Columns: Name, Email, Phone\n\n")
            f.write("Original columns used:\n")
            f.write(f"  First Name: {first_name_col} (renamed to Name)\n")
            f.write(f"  Email: {email_col}\n")
            f.write(f"  Phone: {phone_col if phone_col else 'Not used'}\n\n")
            f.write("Files created:\n")
            for i in range(total_files):
                filename = f"it_depositors_marco_part_{i+1:03d}_of_{total_files:03d}.csv"
                f.write(f"  • {filename}\n")
        
        print(f"\n🎉 SUCCESS! Created {total_files} CSV files!")
        print(f"📁 Location: {output_folder}/")
        print(f"📄 Summary: {summary_file}")
        print(f"\n📧 Perfect for MailSuite email campaigns!")
        print(f"💡 Each file has exactly these columns:")
        print(f"   • Name (from {first_name_col})")
        print(f"   • Email (from {email_col})")
        print(f"   • Phone (from {phone_col if phone_col else 'empty'})")
        print(f"📊 Maximum {rows_per_file} rows per file")
        
        # Show file list
        print(f"\n📋 Created files:")
        for i in range(min(5, total_files)):  # Show first 5 files
            filename = f"it_depositors_marco_part_{i+1:03d}_of_{total_files:03d}.csv"
            filepath = os.path.join(output_folder, filename)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                print(f"   ✅ {filename} ({size:,} bytes)")
        
        if total_files > 5:
            print(f"   ... and {total_files - 5} more files")
        
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        print(f"💡 Make sure the file is not open in Excel and try again.")
        import traceback
        traceback.print_exc()
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
