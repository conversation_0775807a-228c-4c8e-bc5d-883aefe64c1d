#!/usr/bin/env python3
"""
Excel to CSV Splitter
Splits Excel file into multiple CSV files with specified number of rows each
"""

import pandas as pd
import os
import math
from pathlib import Path

def split_excel_to_csv(excel_file, rows_per_file=200, output_folder="csv_splits"):
    """
    Split Excel file into multiple CSV files
    
    Args:
        excel_file (str): Path to Excel file
        rows_per_file (int): Number of rows per CSV file
        output_folder (str): Folder to save CSV files
    """
    
    print(f"📊 Processing Excel file: {excel_file}")
    
    # Check if file exists
    if not os.path.exists(excel_file):
        print(f"❌ Error: File '{excel_file}' not found!")
        return
    
    try:
        # Read Excel file
        print("📖 Reading Excel file...")
        df = pd.read_excel(excel_file)
        
        print(f"✅ File loaded successfully!")
        print(f"📋 Total rows: {len(df)}")
        print(f"📋 Total columns: {len(df.columns)}")
        print(f"📋 Column names: {list(df.columns)}")
        
        # Calculate number of files needed
        total_files = math.ceil(len(df) / rows_per_file)
        print(f"📁 Will create {total_files} CSV files with {rows_per_file} rows each")
        
        # Create output folder
        Path(output_folder).mkdir(exist_ok=True)
        print(f"📁 Created output folder: {output_folder}")
        
        # Split and save files
        for i in range(total_files):
            start_row = i * rows_per_file
            end_row = min((i + 1) * rows_per_file, len(df))
            
            # Get chunk of data
            chunk = df.iloc[start_row:end_row]
            
            # Create filename
            filename = f"Fresh_December9.5k_part_{i+1:03d}.csv"
            filepath = os.path.join(output_folder, filename)
            
            # Save to CSV
            chunk.to_csv(filepath, index=False)
            
            print(f"✅ Created: {filename} (rows {start_row+1}-{end_row}, {len(chunk)} rows)")
        
        print(f"\n🎉 Successfully split into {total_files} CSV files!")
        print(f"📁 Files saved in: {output_folder}/")
        
        # Show file list
        print("\n📋 Created files:")
        for i in range(total_files):
            filename = f"Fresh_December9.5k_part_{i+1:03d}.csv"
            filepath = os.path.join(output_folder, filename)
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                print(f"   • {filename} ({file_size:,} bytes)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        return False

def main():
    print("🚀 Excel to CSV Splitter")
    print("=" * 50)
    
    # Configuration
    excel_file = "Fresh December9.5k.xlsx"
    rows_per_file = 200
    output_folder = "csv_email_lists"
    
    print(f"📄 Input file: {excel_file}")
    print(f"📊 Rows per file: {rows_per_file}")
    print(f"📁 Output folder: {output_folder}")
    print()
    
    # Check if file exists
    if not os.path.exists(excel_file):
        print(f"❌ File '{excel_file}' not found in current directory!")
        print("\n📁 Files in current directory:")
        for file in os.listdir("."):
            if file.endswith(('.xlsx', '.xls', '.csv')):
                print(f"   • {file}")
        
        print(f"\n💡 Please make sure '{excel_file}' is in the same folder as this script.")
        return
    
    # Process the file
    success = split_excel_to_csv(excel_file, rows_per_file, output_folder)
    
    if success:
        print(f"\n✅ All done! Your CSV files are ready for email campaigns.")
        print(f"📧 You can now upload these files to MailSuite for bulk email sending.")
    else:
        print(f"\n❌ Something went wrong. Please check the error messages above.")

if __name__ == "__main__":
    main()
