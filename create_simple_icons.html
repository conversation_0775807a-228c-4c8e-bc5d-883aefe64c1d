<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icons</title>
</head>
<body>
    <h2>Creating Extension Icons</h2>
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    
    <div>
        <button onclick="downloadIcons()">Download Icons</button>
    </div>

    <script>
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Blue background
            ctx.fillStyle = '#1a73e8';
            ctx.fillRect(0, 0, size, size);
            
            // White envelope
            const margin = Math.floor(size / 8);
            const envWidth = size - 2 * margin;
            const envHeight = Math.floor(envWidth * 0.7);
            const x = margin;
            const y = Math.floor((size - envHeight) / 2);
            
            // Envelope body
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(x, y, envWidth, envHeight);
            
            // Envelope flap
            ctx.fillStyle = '#1a73e8';
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x + envWidth / 2, y + envHeight / 3);
            ctx.lineTo(x + envWidth, y);
            ctx.lineTo(x + envWidth, y + envHeight / 3);
            ctx.lineTo(x, y + envHeight / 3);
            ctx.closePath();
            ctx.fill();
            
            // Add "M" for larger icons
            if (size >= 48) {
                ctx.fillStyle = '#ffffff';
                ctx.font = `bold ${Math.floor(size / 4)}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('M', x + envWidth / 2, y + envHeight / 2);
            }
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadIcons() {
            downloadCanvas(document.getElementById('canvas16'), 'icon16.png');
            downloadCanvas(document.getElementById('canvas48'), 'icon48.png');
            downloadCanvas(document.getElementById('canvas128'), 'icon128.png');
        }
        
        // Create icons when page loads
        window.onload = function() {
            createIcon('canvas16', 16);
            createIcon('canvas48', 48);
            createIcon('canvas128', 128);
        };
    </script>
</body>
</html>
