#!/usr/bin/env python3
"""
Advanced Excel to CSV Splitter for Email Campaigns
Automatically detects Excel files and splits them for MailSuite
"""

import pandas as pd
import os
import math
import glob
from pathlib import Path
from datetime import datetime

def find_excel_files():
    """Find all Excel files in current directory"""
    excel_files = []
    patterns = ['*.xlsx', '*.xls', '*.xlsm']
    
    for pattern in patterns:
        excel_files.extend(glob.glob(pattern))
    
    return excel_files

def preview_excel_file(excel_file):
    """Preview Excel file structure"""
    try:
        df = pd.read_excel(excel_file)
        
        print(f"\n📊 File Preview: {excel_file}")
        print("=" * 50)
        print(f"📋 Total rows: {len(df):,}")
        print(f"📋 Total columns: {len(df.columns)}")
        print(f"📋 Columns: {', '.join(df.columns)}")
        
        print(f"\n📖 First 3 rows:")
        print(df.head(3).to_string(index=False))
        
        # Check for email columns
        email_columns = [col for col in df.columns if 'email' in col.lower() or 'mail' in col.lower()]
        if email_columns:
            print(f"\n📧 Email columns found: {', '.join(email_columns)}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return None

def clean_data(df):
    """Clean and prepare data for email campaigns"""
    print("\n🧹 Cleaning data...")
    
    original_rows = len(df)
    
    # Remove empty rows
    df = df.dropna(how='all')
    
    # Find email column
    email_col = None
    for col in df.columns:
        if 'email' in col.lower() or 'mail' in col.lower():
            email_col = col
            break
    
    if email_col:
        # Remove rows without email
        df = df.dropna(subset=[email_col])
        
        # Remove invalid emails (basic check)
        df = df[df[email_col].str.contains('@', na=False)]
        
        print(f"✅ Found email column: {email_col}")
        print(f"📧 Valid emails: {len(df):,}")
    
    removed_rows = original_rows - len(df)
    if removed_rows > 0:
        print(f"🗑️ Removed {removed_rows:,} invalid/empty rows")
    
    return df

def split_excel_to_csv(excel_file, rows_per_file=200, clean_data_flag=True):
    """Split Excel file into multiple CSV files"""
    
    print(f"\n🚀 Processing: {excel_file}")
    
    try:
        # Read Excel file
        df = pd.read_excel(excel_file)
        
        # Clean data if requested
        if clean_data_flag:
            df = clean_data(df)
        
        if len(df) == 0:
            print("❌ No valid data found after cleaning!")
            return False
        
        # Calculate number of files
        total_files = math.ceil(len(df) / rows_per_file)
        
        # Create output folder
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(excel_file)[0]
        output_folder = f"{base_name}_CSV_Split_{timestamp}"
        
        Path(output_folder).mkdir(exist_ok=True)
        
        print(f"\n📁 Creating {total_files} CSV files in: {output_folder}/")
        
        # Split and save
        for i in range(total_files):
            start_row = i * rows_per_file
            end_row = min((i + 1) * rows_per_file, len(df))
            
            chunk = df.iloc[start_row:end_row]
            
            # Create filename
            filename = f"{base_name}_part_{i+1:03d}_of_{total_files:03d}.csv"
            filepath = os.path.join(output_folder, filename)
            
            # Save to CSV
            chunk.to_csv(filepath, index=False)
            
            print(f"✅ {filename} - {len(chunk)} rows")
        
        # Create summary file
        summary_file = os.path.join(output_folder, "SUMMARY.txt")
        with open(summary_file, 'w') as f:
            f.write(f"Email Campaign Files Summary\n")
            f.write(f"=" * 40 + "\n\n")
            f.write(f"Source file: {excel_file}\n")
            f.write(f"Total rows: {len(df):,}\n")
            f.write(f"Rows per file: {rows_per_file}\n")
            f.write(f"Total CSV files: {total_files}\n")
            f.write(f"Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"Columns: {', '.join(df.columns)}\n\n")
            f.write(f"Files created:\n")
            for i in range(total_files):
                filename = f"{base_name}_part_{i+1:03d}_of_{total_files:03d}.csv"
                f.write(f"  • {filename}\n")
        
        print(f"\n🎉 Successfully created {total_files} CSV files!")
        print(f"📁 Location: {output_folder}/")
        print(f"📄 Summary: {summary_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🚀 Advanced Excel to CSV Splitter for Email Campaigns")
    print("=" * 60)
    
    # Find Excel files
    excel_files = find_excel_files()
    
    if not excel_files:
        print("❌ No Excel files found in current directory!")
        print("\n💡 Please place your Excel file in the same folder as this script.")
        print("   Supported formats: .xlsx, .xls, .xlsm")
        return
    
    print(f"\n📁 Found {len(excel_files)} Excel file(s):")
    for i, file in enumerate(excel_files, 1):
        size = os.path.getsize(file) / 1024 / 1024  # MB
        print(f"   {i}. {file} ({size:.1f} MB)")
    
    # Select file
    if len(excel_files) == 1:
        selected_file = excel_files[0]
        print(f"\n✅ Auto-selected: {selected_file}")
    else:
        while True:
            try:
                choice = input(f"\nSelect file (1-{len(excel_files)}): ")
                index = int(choice) - 1
                if 0 <= index < len(excel_files):
                    selected_file = excel_files[index]
                    break
                else:
                    print("❌ Invalid choice!")
            except ValueError:
                print("❌ Please enter a number!")
    
    # Preview file
    df = preview_excel_file(selected_file)
    if df is None:
        return
    
    # Get settings
    print(f"\n⚙️ Configuration:")
    
    # Rows per file
    while True:
        try:
            rows_input = input(f"Rows per CSV file (default: 200): ").strip()
            if not rows_input:
                rows_per_file = 200
                break
            rows_per_file = int(rows_input)
            if rows_per_file > 0:
                break
            else:
                print("❌ Please enter a positive number!")
        except ValueError:
            print("❌ Please enter a valid number!")
    
    # Clean data option
    clean_input = input(f"Clean data (remove empty rows, invalid emails)? (Y/n): ").strip().lower()
    clean_data_flag = clean_input != 'n'
    
    print(f"\n📊 Settings:")
    print(f"   • File: {selected_file}")
    print(f"   • Rows per file: {rows_per_file}")
    print(f"   • Clean data: {'Yes' if clean_data_flag else 'No'}")
    print(f"   • Estimated files: {math.ceil(len(df) / rows_per_file)}")
    
    # Confirm
    confirm = input(f"\nProceed? (Y/n): ").strip().lower()
    if confirm == 'n':
        print("❌ Cancelled by user.")
        return
    
    # Process file
    success = split_excel_to_csv(selected_file, rows_per_file, clean_data_flag)
    
    if success:
        print(f"\n✅ All done! Your CSV files are ready for MailSuite email campaigns.")
        print(f"💡 Upload these CSV files to your MailSuite extension for bulk email sending.")
    else:
        print(f"\n❌ Processing failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
