# VPS Email Server Setup for 10K Emails/Day

Since you have a VPS, this is actually a MUCH better approach than using Gmail accounts! Here's how to set up a professional email sending system on your VPS.

## Option 1: Postal Mail Server (Recommended)

Postal is an open-source mail server designed for high-volume sending.

### Step 1: VPS Requirements
- **Minimum**: 2GB RAM, 2 CPU cores, 20GB storage
- **Recommended**: 4GB RAM, 4 CPU cores, 40GB storage
- **OS**: Ubuntu 20.04 or 22.04
- **Static IP**: Essential for email reputation

### Step 2: Install Postal

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Clone Postal
git clone https://github.com/postalserver/postal.git
cd postal

# Install Postal
sudo docker-compose up -d
```

### Step 3: Configure DNS Records

For your domain (e.g., yourdomain.com), add these DNS records:

```
# A Record
mail.yourdomain.com    A    YOUR_VPS_IP

# MX Record  
yourdomain.com         MX   10 mail.yourdomain.com

# SPF Record
yourdomain.com         TXT  "v=spf1 ip4:YOUR_VPS_IP ~all"

# DMARC Record
_dmarc.yourdomain.com  TXT  "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"

# PTR Record (Reverse DNS) - Contact your VPS provider
YOUR_VPS_IP            PTR  mail.yourdomain.com
```

### Step 4: Postal Configuration

```bash
# Access Postal web interface
# Go to http://YOUR_VPS_IP:5000

# Create organization and mail server
# Add your domain
# Generate DKIM keys and add to DNS
```

## Option 2: Simple SMTP Server with Python

If you want a simpler solution, here's a Python-based SMTP server:

### Step 1: Install Dependencies

```bash
sudo apt update
sudo apt install python3 python3-pip postfix
pip3 install flask smtplib email-validator
```

### Step 2: Configure Postfix

```bash
# Edit main configuration
sudo nano /etc/postfix/main.cf

# Add these lines:
myhostname = mail.yourdomain.com
mydomain = yourdomain.com
myorigin = $mydomain
inet_interfaces = all
mydestination = $myhostname, localhost.$mydomain, localhost, $mydomain
relayhost = 
mynetworks = *********/8 [::ffff:*********]/104 [::1]/128 YOUR_VPS_IP/32

# Restart Postfix
sudo systemctl restart postfix
sudo systemctl enable postfix
```

### Step 3: Create Email Sending API

```python
# email_server.py
from flask import Flask, request, jsonify
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import time
import threading
import queue
import logging

app = Flask(__name__)

# Email queue for batch processing
email_queue = queue.Queue()

class EmailSender:
    def __init__(self):
        self.smtp_server = "localhost"
        self.smtp_port = 587
        self.is_running = False
        
    def send_email(self, to_email, subject, body, from_email="<EMAIL>"):
        try:
            msg = MIMEMultipart()
            msg['From'] = from_email
            msg['To'] = to_email
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'html'))
            
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.send_message(msg)
            server.quit()
            
            return True
        except Exception as e:
            logging.error(f"Failed to send email to {to_email}: {e}")
            return False
    
    def process_queue(self):
        while self.is_running:
            try:
                email_data = email_queue.get(timeout=1)
                success = self.send_email(**email_data)
                
                if success:
                    logging.info(f"Email sent to {email_data['to_email']}")
                else:
                    logging.error(f"Failed to send email to {email_data['to_email']}")
                
                # Delay between emails (adjust as needed)
                time.sleep(2)
                
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Queue processing error: {e}")

# Global email sender
sender = EmailSender()

@app.route('/send-email', methods=['POST'])
def send_email():
    data = request.json
    
    required_fields = ['to_email', 'subject', 'body']
    if not all(field in data for field in required_fields):
        return jsonify({'error': 'Missing required fields'}), 400
    
    # Add to queue
    email_queue.put({
        'to_email': data['to_email'],
        'subject': data['subject'],
        'body': data['body'],
        'from_email': data.get('from_email', '<EMAIL>')
    })
    
    return jsonify({'status': 'queued', 'queue_size': email_queue.qsize()})

@app.route('/send-bulk', methods=['POST'])
def send_bulk():
    data = request.json
    
    if 'emails' not in data:
        return jsonify({'error': 'No emails provided'}), 400
    
    count = 0
    for email_data in data['emails']:
        if all(field in email_data for field in ['to_email', 'subject', 'body']):
            email_queue.put(email_data)
            count += 1
    
    return jsonify({'status': 'queued', 'emails_queued': count, 'queue_size': email_queue.qsize()})

@app.route('/status', methods=['GET'])
def status():
    return jsonify({
        'queue_size': email_queue.qsize(),
        'is_running': sender.is_running
    })

@app.route('/start', methods=['POST'])
def start_sender():
    if not sender.is_running:
        sender.is_running = True
        thread = threading.Thread(target=sender.process_queue)
        thread.daemon = True
        thread.start()
        return jsonify({'status': 'started'})
    return jsonify({'status': 'already_running'})

@app.route('/stop', methods=['POST'])
def stop_sender():
    sender.is_running = False
    return jsonify({'status': 'stopped'})

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    app.run(host='0.0.0.0', port=5000)
```

### Step 4: Run the Email Server

```bash
# Start the email server
python3 email_server.py

# Test it
curl -X POST http://YOUR_VPS_IP:5000/start
curl -X POST http://YOUR_VPS_IP:5000/send-email \
  -H "Content-Type: application/json" \
  -d '{"to_email": "<EMAIL>", "subject": "Test", "body": "Hello World"}'
```

## Step 3: Modify Chrome Extension for VPS

Now let's modify the extension to work with your VPS instead of Gmail:

```javascript
// Add this to content/mailsuite-content.js

class VPSEmailSender {
    constructor(vpsUrl) {
        this.vpsUrl = vpsUrl; // e.g., 'http://YOUR_VPS_IP:5000'
    }
    
    async sendEmail(emailData) {
        try {
            const response = await fetch(`${this.vpsUrl}/send-email`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(emailData)
            });
            
            return await response.json();
        } catch (error) {
            console.error('Error sending email:', error);
            return { error: error.message };
        }
    }
    
    async sendBulkEmails(emailsArray) {
        try {
            const response = await fetch(`${this.vpsUrl}/send-bulk`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ emails: emailsArray })
            });
            
            return await response.json();
        } catch (error) {
            console.error('Error sending bulk emails:', error);
            return { error: error.message };
        }
    }
    
    async getStatus() {
        try {
            const response = await fetch(`${this.vpsUrl}/status`);
            return await response.json();
        } catch (error) {
            console.error('Error getting status:', error);
            return { error: error.message };
        }
    }
}

// Usage in your extension
const vpsSender = new VPSEmailSender('http://YOUR_VPS_IP:5000');

// Instead of using Gmail automation, send via VPS
async function startVPSCampaign(campaign) {
    const emails = campaign.contacts.map(contact => ({
        to_email: contact.email,
        subject: personalizeText(campaign.subject, contact),
        body: personalizeText(campaign.body, contact),
        from_email: `${campaign.fromName} <<EMAIL>>`
    }));
    
    const result = await vpsSender.sendBulkEmails(emails);
    console.log('Campaign started:', result);
}
```

## Benefits of VPS Setup vs Gmail

### Advantages:
1. **No daily limits** - Send as many emails as your server can handle
2. **Better deliverability** - Dedicated IP and proper authentication
3. **Full control** - Complete control over sending infrastructure
4. **Cost effective** - No per-email charges after setup
5. **Professional** - Your own domain and branding
6. **Scalable** - Can handle 10K+ emails easily

### Setup Costs:
- **VPS**: $10-50/month (depending on specs)
- **Domain**: $10/year
- **Setup time**: 2-4 hours

## Quick Start Commands

```bash
# 1. Set up VPS with Postal (recommended)
git clone https://github.com/postalserver/postal.git
cd postal
sudo docker-compose up -d

# 2. Or set up simple Python server
sudo apt install python3 python3-pip postfix
pip3 install flask
python3 email_server.py

# 3. Configure DNS records (essential!)
# Add A, MX, SPF, DKIM, DMARC records

# 4. Test sending
curl -X POST http://YOUR_VPS_IP:5000/send-email \
  -H "Content-Type: application/json" \
  -d '{"to_email": "<EMAIL>", "subject": "Test", "body": "Hello"}'
```

This VPS approach is much more professional and scalable than using multiple Gmail accounts. You'll have better deliverability and no daily limits!

Would you like me to help you set up any specific part of this VPS email system?
