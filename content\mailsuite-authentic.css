/* Authentic MailSuite Styling - Exact Visual Replica */

/* Sidebar Integration */
.mailsuite-sidebar-item {
  cursor: pointer !important;
  transition: background-color 0.2s ease;
}

.mailsuite-sidebar-item:hover {
  background-color: #f1f3f4 !important;
}

.mailsuite-text {
  font-size: 14px;
  color: #202124;
  font-weight: 500;
}

/* Compose Button */
.mailsuite-compose-btn {
  margin-left: 8px !important;
  background: #f8f9fa !important;
  border: 1px solid #dadce0 !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  font-size: 13px !important;
  color: #3c4043 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.mailsuite-compose-btn:hover {
  background: #e8f0fe !important;
  border-color: #1a73e8 !important;
  color: #1a73e8 !important;
}

.mailsuite-btn-text {
  font-weight: 500;
}

/* Main Panel */
.mailsuite-panel {
  background: #ffffff;
  border: 1px solid #dadce0;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
  font-family: 'Google Sans', Roboto, Arial, sans-serif;
  font-size: 14px;
  color: #202124;
  width: 100%;
  max-width: 100%;
  z-index: 1000;
  position: relative;
}

/* Panel Header */
.mailsuite-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #4285f4, #34a853);
  color: white;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #dadce0;
}

.mailsuite-logo {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
}

.mailsuite-icon {
  margin-right: 8px;
  font-size: 18px;
}

.mailsuite-panel-controls {
  display: flex;
  gap: 4px;
}

.mailsuite-minimize,
.mailsuite-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: background-color 0.2s ease;
}

.mailsuite-minimize:hover,
.mailsuite-close:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Panel Body */
.mailsuite-panel-body {
  padding: 0;
}

/* Tabs */
.mailsuite-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dadce0;
}

.mailsuite-tab {
  flex: 1;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  font-weight: 500;
  color: #5f6368;
}

.mailsuite-tab:hover {
  background: #e8f0fe;
  color: #1a73e8;
}

.mailsuite-tab.active {
  background: white;
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

.mailsuite-tab-icon {
  margin-right: 6px;
  font-size: 14px;
}

/* Tab Content */
.mailsuite-tab-content {
  padding: 16px;
}

.mailsuite-tab-pane {
  display: none;
}

.mailsuite-tab-pane.active {
  display: block;
}

/* Sections */
.mailsuite-section {
  margin-bottom: 20px;
}

.mailsuite-section:last-child {
  margin-bottom: 0;
}

.mailsuite-section-header {
  margin-bottom: 12px;
}

.mailsuite-section-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #202124;
  display: flex;
  align-items: center;
}

/* Upload Area */
.mailsuite-upload-area {
  margin-bottom: 16px;
}

.mailsuite-upload-box {
  border: 2px dashed #dadce0;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fafbfc;
}

.mailsuite-upload-box:hover {
  border-color: #1a73e8;
  background: #e8f0fe;
}

.mailsuite-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mailsuite-upload-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.7;
}

.mailsuite-upload-text {
  color: #5f6368;
  line-height: 1.4;
}

.mailsuite-upload-status {
  margin-top: 8px;
  font-size: 13px;
  color: #1a73e8;
  font-weight: 500;
}

/* Variables */
.mailsuite-variables {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 6px;
  padding: 12px;
}

.mailsuite-variables-header {
  font-size: 13px;
  color: #5f6368;
  margin-bottom: 8px;
  font-weight: 500;
}

.mailsuite-variables-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.mailsuite-variable {
  background: #e8f0fe;
  color: #1a73e8;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-family: 'Roboto Mono', monospace;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #dadce0;
}

.mailsuite-variable:hover {
  background: #1a73e8;
  color: white;
  transform: translateY(-1px);
}

/* Options */
.mailsuite-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mailsuite-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #202124;
}

.mailsuite-checkbox input[type="checkbox"] {
  display: none;
}

.mailsuite-checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #dadce0;
  border-radius: 3px;
  margin-right: 8px;
  position: relative;
  transition: all 0.2s ease;
}

.mailsuite-checkbox input[type="checkbox"]:checked + .mailsuite-checkmark {
  background: #1a73e8;
  border-color: #1a73e8;
}

.mailsuite-checkbox input[type="checkbox"]:checked + .mailsuite-checkmark::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Input Groups */
.mailsuite-input-group {
  margin-bottom: 12px;
}

.mailsuite-input-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 13px;
  font-weight: 500;
  color: #202124;
}

.mailsuite-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.mailsuite-input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.mailsuite-input::placeholder {
  color: #9aa0a6;
}

/* Subdomain Config */
.mailsuite-subdomain-config {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 6px;
  padding: 12px;
  margin-top: 8px;
}

/* Buttons */
.mailsuite-action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.mailsuite-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.mailsuite-btn-primary {
  background: #1a73e8;
  color: white;
}

.mailsuite-btn-primary:hover {
  background: #1765cc;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
}

.mailsuite-btn-secondary {
  background: #f8f9fa;
  color: #1a73e8;
  border: 1px solid #dadce0;
}

.mailsuite-btn-secondary:hover {
  background: #e8f0fe;
  border-color: #1a73e8;
}

.mailsuite-btn-icon {
  font-size: 14px;
}

/* Stats Grid */
.mailsuite-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.mailsuite-stat-card {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 6px;
  padding: 16px;
  text-align: center;
}

.mailsuite-stat-number {
  font-size: 24px;
  font-weight: 500;
  color: #1a73e8;
  margin-bottom: 4px;
}

.mailsuite-stat-label {
  font-size: 12px;
  color: #5f6368;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Templates */
.mailsuite-templates-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dadce0;
  border-radius: 4px;
  margin-bottom: 12px;
}

.mailsuite-no-templates {
  padding: 20px;
  text-align: center;
  color: #5f6368;
  font-style: italic;
}

.mailsuite-template-actions {
  display: flex;
  gap: 8px;
}

.mailsuite-template-actions input {
  flex: 1;
}

/* Preview Modal */
.mailsuite-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mailsuite-preview-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.mailsuite-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #dadce0;
}

.mailsuite-preview-header h3 {
  margin: 0;
  font-size: 16px;
  color: #202124;
}

.mailsuite-preview-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #5f6368;
  padding: 4px;
  border-radius: 4px;
}

.mailsuite-preview-close:hover {
  background: #f1f3f4;
}

.mailsuite-preview-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.mailsuite-preview-field {
  margin-bottom: 16px;
}

.mailsuite-preview-message {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
}

/* Responsive */
@media (max-width: 768px) {
  .mailsuite-panel {
    margin: 8px 0;
  }
  
  .mailsuite-tabs {
    flex-wrap: wrap;
  }
  
  .mailsuite-tab {
    flex: 1 1 50%;
    min-width: 120px;
  }
  
  .mailsuite-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .mailsuite-action-buttons {
    flex-direction: column;
  }
}

/* Animation */
@keyframes mailsuite-fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mailsuite-panel {
  animation: mailsuite-fadeIn 0.3s ease-out;
}

/* Loading States */
.mailsuite-loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1a73e8;
  border-radius: 50%;
  animation: mailsuite-spin 1s linear infinite;
}

@keyframes mailsuite-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
