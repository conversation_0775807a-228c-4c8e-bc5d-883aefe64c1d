// MailSuite Popup Script
class MailSuitePopup {
  constructor() {
    this.currentTab = 'dashboard';
    this.activeCampaign = null;
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadDashboardData();
    this.startPeriodicUpdates();
  }

  setupEventListeners() {
    // Tab switching
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });

    // Dashboard actions
    document.getElementById('open-gmail').addEventListener('click', () => {
      chrome.tabs.create({ url: 'https://mail.google.com' });
    });

    document.getElementById('new-campaign').addEventListener('click', () => {
      chrome.tabs.create({ url: 'https://mail.google.com/mail/u/0/#inbox?compose=new' });
    });

    // Campaign controls
    document.getElementById('pause-campaign').addEventListener('click', () => {
      this.pauseActiveCampaign();
    });

    document.getElementById('stop-campaign').addEventListener('click', () => {
      this.stopActiveCampaign();
    });

    // Campaigns tab
    document.getElementById('refresh-campaigns').addEventListener('click', () => {
      this.loadCampaigns();
    });

    // Analytics
    document.getElementById('analytics-period').addEventListener('change', () => {
      this.loadAnalytics();
    });

    // Settings
    document.getElementById('save-settings').addEventListener('click', () => {
      this.saveSettings();
    });

    document.getElementById('export-data').addEventListener('click', () => {
      this.exportData();
    });

    document.getElementById('clear-analytics').addEventListener('click', () => {
      this.clearAnalytics();
    });

    document.getElementById('reset-settings').addEventListener('click', () => {
      this.resetSettings();
    });
  }

  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab').forEach(tab => {
      tab.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"].tab-content`).classList.add('active');

    this.currentTab = tabName;

    // Load tab-specific data
    switch (tabName) {
      case 'campaigns':
        this.loadCampaigns();
        break;
      case 'analytics':
        this.loadAnalytics();
        break;
      case 'settings':
        this.loadSettings();
        break;
    }
  }

  async loadDashboardData() {
    try {
      // Load quick stats
      const result = await chrome.storage.local.get(['campaigns', 'trackingEvents']);
      const campaigns = result.campaigns || {};
      const events = result.trackingEvents || [];

      let totalSent = 0;
      let totalOpens = 0;
      let activeCampaign = null;

      Object.values(campaigns).forEach(campaign => {
        totalSent += campaign.sentCount || 0;
        totalOpens += campaign.openCount || 0;
        
        if (campaign.status === 'active') {
          activeCampaign = campaign;
        }
      });

      // Update quick stats
      document.getElementById('total-sent').textContent = totalSent.toLocaleString();
      document.getElementById('total-opens').textContent = totalOpens.toLocaleString();
      
      const openRate = totalSent > 0 ? ((totalOpens / totalSent) * 100).toFixed(1) : 0;
      document.getElementById('open-rate').textContent = `${openRate}%`;

      // Update active campaign
      this.updateActiveCampaign(activeCampaign);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  }

  updateActiveCampaign(campaign) {
    const activeCampaignElement = document.getElementById('active-campaign');
    
    if (campaign) {
      this.activeCampaign = campaign;
      activeCampaignElement.style.display = 'block';
      
      document.getElementById('campaign-subject').textContent = campaign.subject;
      
      const progress = campaign.currentIndex || 0;
      const total = campaign.contacts ? campaign.contacts.length : 0;
      const percentage = total > 0 ? (progress / total) * 100 : 0;
      
      document.getElementById('progress-fill').style.width = `${percentage}%`;
      document.getElementById('progress-text').textContent = `${progress} / ${total} sent`;
      
    } else {
      this.activeCampaign = null;
      activeCampaignElement.style.display = 'none';
    }
  }

  async pauseActiveCampaign() {
    if (!this.activeCampaign) return;
    
    try {
      await chrome.runtime.sendMessage({
        type: 'pauseCampaign',
        campaignId: this.activeCampaign.id
      });
      
      this.loadDashboardData();
    } catch (error) {
      console.error('Error pausing campaign:', error);
    }
  }

  async stopActiveCampaign() {
    if (!this.activeCampaign) return;
    
    if (confirm('Are you sure you want to stop this campaign? This cannot be undone.')) {
      try {
        await chrome.runtime.sendMessage({
          type: 'stopCampaign',
          campaignId: this.activeCampaign.id
        });
        
        this.loadDashboardData();
      } catch (error) {
        console.error('Error stopping campaign:', error);
      }
    }
  }

  async loadCampaigns() {
    try {
      const result = await chrome.storage.local.get(['campaigns']);
      const campaigns = result.campaigns || {};
      
      const campaignsList = document.getElementById('campaigns-list');
      campaignsList.innerHTML = '';
      
      const campaignArray = Object.values(campaigns).sort((a, b) => 
        new Date(b.startedAt || b.created) - new Date(a.startedAt || a.created)
      );
      
      if (campaignArray.length === 0) {
        campaignsList.innerHTML = '<p style="text-align: center; color: #5f6368;">No campaigns yet. Create your first campaign in Gmail!</p>';
        return;
      }
      
      campaignArray.forEach(campaign => {
        const campaignElement = document.createElement('div');
        campaignElement.className = 'campaign-item';
        
        const statusClass = campaign.status || 'unknown';
        const sentCount = campaign.sentCount || 0;
        const totalCount = campaign.contacts ? campaign.contacts.length : 0;
        const openCount = campaign.openCount || 0;
        const openRate = sentCount > 0 ? ((openCount / sentCount) * 100).toFixed(1) : 0;
        
        campaignElement.innerHTML = `
          <h4>${campaign.subject}</h4>
          <p><span class="campaign-status ${statusClass}">${statusClass}</span></p>
          <p>Sent: ${sentCount} / ${totalCount}</p>
          <p>Opens: ${openCount} (${openRate}%)</p>
          <p>Created: ${new Date(campaign.startedAt || campaign.created).toLocaleDateString()}</p>
        `;
        
        campaignsList.appendChild(campaignElement);
      });
      
    } catch (error) {
      console.error('Error loading campaigns:', error);
    }
  }

  async loadAnalytics() {
    try {
      const period = parseInt(document.getElementById('analytics-period').value);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - period);
      
      const result = await chrome.storage.local.get(['campaigns', 'trackingEvents']);
      const campaigns = result.campaigns || {};
      const events = result.trackingEvents || [];
      
      // Filter campaigns by date
      const recentCampaigns = Object.values(campaigns).filter(campaign => {
        const campaignDate = new Date(campaign.startedAt || campaign.created);
        return campaignDate >= cutoffDate;
      });
      
      // Calculate metrics
      let totalSent = 0;
      let totalOpens = 0;
      let totalClicks = 0;
      
      recentCampaigns.forEach(campaign => {
        totalSent += campaign.sentCount || 0;
        totalOpens += campaign.openCount || 0;
        totalClicks += campaign.clickCount || 0;
      });
      
      const openRate = totalSent > 0 ? ((totalOpens / totalSent) * 100).toFixed(1) : 0;
      const clickRate = totalSent > 0 ? ((totalClicks / totalSent) * 100).toFixed(1) : 0;
      
      // Update analytics display
      document.getElementById('analytics-sent').textContent = totalSent.toLocaleString();
      document.getElementById('analytics-opens').textContent = totalOpens.toLocaleString();
      document.getElementById('analytics-clicks').textContent = totalClicks.toLocaleString();
      document.getElementById('analytics-open-rate').textContent = `${openRate}%`;
      document.getElementById('analytics-click-rate').textContent = `${clickRate}%`;
      
      // Load recent activity
      this.loadRecentActivity(events, cutoffDate);
      
    } catch (error) {
      console.error('Error loading analytics:', error);
    }
  }

  loadRecentActivity(events, cutoffDate) {
    const recentEvents = events
      .filter(event => new Date(event.timestamp) >= cutoffDate)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, 10);
    
    const activityList = document.getElementById('recent-activity');
    activityList.innerHTML = '';
    
    if (recentEvents.length === 0) {
      activityList.innerHTML = '<p style="color: #5f6368; font-size: 12px;">No recent activity</p>';
      return;
    }
    
    recentEvents.forEach(event => {
      const activityItem = document.createElement('div');
      activityItem.className = 'activity-item';
      
      const eventTime = new Date(event.timestamp).toLocaleString();
      const eventType = event.type === 'open' ? 'Email opened' : 'Link clicked';
      
      activityItem.innerHTML = `
        <div>${eventType}</div>
        <div class="activity-time">${eventTime}</div>
      `;
      
      activityList.appendChild(activityItem);
    });
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['mailsuiteSettings']);
      const settings = result.mailsuiteSettings || {};
      
      // Load default settings
      document.getElementById('default-delay').value = (settings.defaultDelay || 60000) / 1000;
      document.getElementById('default-batch-size').value = settings.defaultBatchSize || 20;
      document.getElementById('tracking-enabled').checked = settings.trackingEnabled !== false;
      document.getElementById('subdomain-rotation-enabled').checked = settings.subdomainRotation || false;
      document.getElementById('tracking-domain').value = settings.trackingDomain || '';
      
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  async saveSettings() {
    try {
      const settings = {
        defaultDelay: parseInt(document.getElementById('default-delay').value) * 1000,
        defaultBatchSize: parseInt(document.getElementById('default-batch-size').value),
        trackingEnabled: document.getElementById('tracking-enabled').checked,
        subdomainRotation: document.getElementById('subdomain-rotation-enabled').checked,
        trackingDomain: document.getElementById('tracking-domain').value
      };
      
      await chrome.storage.local.set({ mailsuiteSettings: settings });
      
      // Show success message
      const button = document.getElementById('save-settings');
      const originalText = button.textContent;
      button.textContent = 'Saved!';
      button.style.background = '#137333';
      
      setTimeout(() => {
        button.textContent = originalText;
        button.style.background = '';
      }, 2000);
      
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }

  async exportData() {
    try {
      const result = await chrome.storage.local.get(['campaigns', 'trackingEvents']);
      const data = {
        campaigns: result.campaigns || {},
        trackingEvents: result.trackingEvents || [],
        exportDate: new Date().toISOString()
      };
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `mailsuite-data-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  }

  async clearAnalytics() {
    if (confirm('Are you sure you want to clear all analytics data? This cannot be undone.')) {
      try {
        await chrome.storage.local.remove(['trackingEvents']);
        
        // Reset campaign analytics
        const result = await chrome.storage.local.get(['campaigns']);
        const campaigns = result.campaigns || {};
        
        Object.values(campaigns).forEach(campaign => {
          campaign.openCount = 0;
          campaign.clickCount = 0;
        });
        
        await chrome.storage.local.set({ campaigns });
        
        this.loadAnalytics();
        this.loadDashboardData();
        
      } catch (error) {
        console.error('Error clearing analytics:', error);
      }
    }
  }

  async resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default?')) {
      try {
        await chrome.storage.local.remove(['mailsuiteSettings']);
        this.loadSettings();
        
      } catch (error) {
        console.error('Error resetting settings:', error);
      }
    }
  }

  startPeriodicUpdates() {
    // Update dashboard every 5 seconds
    setInterval(() => {
      if (this.currentTab === 'dashboard') {
        this.loadDashboardData();
      }
    }, 5000);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new MailSuitePopup();
});
