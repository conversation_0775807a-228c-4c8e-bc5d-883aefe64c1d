#!/bin/bash

# MailSuite VPS Setup for help-coinmarcetcap.com
# VPS IP: ***************

echo "🚀 MailSuite Setup for help-coinmarcetcap.com"
echo "============================================="
echo "VPS IP: ***************"
echo "Domain: help-coinmarcetcap.com"
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
DOMAIN="help-coinmarcetcap.com"
VPS_IP="***************"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ Please run this script as root (use sudo)${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Configuration confirmed:${NC}"
echo "   Domain: $DOMAIN"
echo "   VPS IP: $VPS_IP"
echo "   Mail server: mail.$DOMAIN"
echo "   PTR Record: Already configured ✅"
echo ""

read -p "Continue with installation? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
fi

# Update system
echo -e "${BLUE}📦 Updating system packages...${NC}"
apt update && apt upgrade -y

# Install required packages
echo -e "${BLUE}📦 Installing required packages...${NC}"
apt install -y python3 python3-pip postfix mailutils ufw fail2ban opendkim opendkim-tools curl wget

# Install Python packages
echo -e "${BLUE}🐍 Installing Python packages...${NC}"
pip3 install flask

# Configure firewall
echo -e "${BLUE}🔥 Configuring firewall...${NC}"
ufw allow 22    # SSH
ufw allow 25    # SMTP
ufw allow 587   # SMTP submission
ufw allow 5000  # Web dashboard
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw --force enable

# Configure Postfix
echo -e "${BLUE}📧 Configuring Postfix for $DOMAIN...${NC}"
cp /etc/postfix/main.cf /etc/postfix/main.cf.backup

cat > /etc/postfix/main.cf << EOF
# MailSuite Configuration for help-coinmarcetcap.com
myhostname = mail.help-coinmarcetcap.com
mydomain = help-coinmarcetcap.com
myorigin = \$mydomain
inet_interfaces = all
inet_protocols = ipv4
mydestination = \$myhostname, localhost.\$mydomain, localhost, \$mydomain

# Network configuration
mynetworks = *********/8 [::ffff:*********]/104 [::1]/128 ***************/32
relayhost = 

# Mail delivery
home_mailbox = Maildir/
mailbox_command = 

# Security
smtpd_banner = \$myhostname ESMTP
biff = no
append_dot_mydomain = no
readme_directory = no

# TLS configuration
smtpd_use_tls = yes
smtpd_tls_cert_file = /etc/ssl/certs/ssl-cert-snakeoil.pem
smtpd_tls_key_file = /etc/ssl/private/ssl-cert-snakeoil.key
smtpd_tls_security_level = may
smtp_tls_security_level = may

# SMTP restrictions
smtpd_relay_restrictions = permit_mynetworks permit_sasl_authenticated defer_unauth_destination
smtpd_recipient_restrictions = permit_mynetworks permit_sasl_authenticated reject_unauth_destination

# Message size limit
message_size_limit = 52428800
mailbox_size_limit = 0
recipient_delimiter = +

# DKIM configuration
milter_protocol = 2
milter_default_action = accept
smtpd_milters = inet:localhost:12301
non_smtpd_milters = inet:localhost:12301
EOF

# Configure DKIM
echo -e "${BLUE}🔐 Configuring DKIM for $DOMAIN...${NC}"
mkdir -p /etc/opendkim/keys/$DOMAIN
cd /etc/opendkim/keys/$DOMAIN
opendkim-genkey -t -s default -d $DOMAIN

chown opendkim:opendkim /etc/opendkim/keys/$DOMAIN/default.private
chmod 600 /etc/opendkim/keys/$DOMAIN/default.private

cat > /etc/opendkim.conf << EOF
AutoRestart             Yes
AutoRestartRate         10/1h
UMask                   002
Syslog                  yes
SyslogSuccess           Yes
LogWhy                  Yes
Canonicalization        relaxed/simple
ExternalIgnoreList      refile:/etc/opendkim/TrustedHosts
InternalHosts           refile:/etc/opendkim/TrustedHosts
KeyTable                refile:/etc/opendkim/KeyTable
SigningTable            refile:/etc/opendkim/SigningTable
Mode                    sv
PidFile                 /var/run/opendkim/opendkim.pid
SignatureAlgorithm      rsa-sha256
UserID                  opendkim:opendkim
Socket                  inet:12301@localhost
EOF

echo "127.0.0.1" > /etc/opendkim/TrustedHosts
echo "localhost" >> /etc/opendkim/TrustedHosts
echo "$DOMAIN" >> /etc/opendkim/TrustedHosts
echo "mail.$DOMAIN" >> /etc/opendkim/TrustedHosts

echo "default._domainkey.$DOMAIN $DOMAIN:default:/etc/opendkim/keys/$DOMAIN/default.private" > /etc/opendkim/KeyTable
echo "*@$DOMAIN default._domainkey.$DOMAIN" > /etc/opendkim/SigningTable

# Create email server directory
echo -e "${BLUE}📁 Setting up MailSuite email server...${NC}"
mkdir -p /opt/mailsuite-server
cd /opt/mailsuite-server

# Create Python email server
cat > /opt/mailsuite-server/server.py << 'EOF'
#!/usr/bin/env python3
"""
MailSuite Email Server for help-coinmarcetcap.com
High-volume email sending with web dashboard
"""

from flask import Flask, request, jsonify, render_template_string
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import time
import threading
import queue
import logging
import json
from datetime import datetime
import uuid

app = Flask(__name__)

# Configuration for help-coinmarcetcap.com
SMTP_SERVER = "localhost"
SMTP_PORT = 25
DOMAIN = "help-coinmarcetcap.com"
VPS_IP = "***************"
DEFAULT_FROM_EMAIL = f"noreply@{DOMAIN}"
MAX_EMAILS_PER_HOUR = 2000
DELAY_BETWEEN_EMAILS = 2

# Email queue and statistics
email_queue = queue.Queue()
sent_count = 0
failed_count = 0
is_running = False

# Enhanced web dashboard
DASHBOARD_HTML = '''
<!DOCTYPE html>
<html>
<head>
    <title>MailSuite Dashboard - help-coinmarcetcap.com</title>
    <style>
        body { 
            font-family: 'Google Sans', Arial, sans-serif; 
            margin: 0; 
            background: linear-gradient(135deg, #4285f4, #34a853); 
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header { 
            background: rgba(255,255,255,0.95); 
            padding: 30px; 
            border-radius: 12px; 
            text-align: center; 
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header h1 { 
            margin: 0; 
            color: #202124; 
            font-size: 28px; 
            font-weight: 500; 
        }
        .header p { 
            margin: 10px 0 0 0; 
            color: #5f6368; 
            font-size: 16px; 
        }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            margin: 20px 0; 
        }
        .stat { 
            background: rgba(255,255,255,0.95); 
            padding: 25px; 
            border-radius: 12px; 
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        .stat:hover {
            transform: translateY(-2px);
        }
        .stat h3 { 
            margin: 0; 
            color: #4285f4; 
            font-size: 32px; 
            font-weight: 500;
        }
        .stat p { 
            margin: 8px 0 0 0; 
            color: #5f6368; 
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .section { 
            background: rgba(255,255,255,0.95); 
            margin: 20px 0; 
            padding: 30px; 
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .section h3 { 
            margin: 0 0 20px 0; 
            color: #202124; 
            font-size: 20px;
            font-weight: 500;
        }
        .button { 
            padding: 12px 24px; 
            background: #4285f4; 
            color: white; 
            border: none; 
            border-radius: 6px; 
            font-size: 14px;
            font-weight: 500;
            cursor: pointer; 
            margin: 8px 8px 8px 0; 
            transition: all 0.2s ease;
        }
        .button:hover { 
            background: #3367d6; 
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
        }
        .button.success { background: #34a853; }
        .button.success:hover { background: #2d8f47; }
        .button.danger { background: #ea4335; }
        .button.danger:hover { background: #d33b2c; }
        .form-group { 
            margin: 20px 0; 
        }
        .form-group label { 
            display: block; 
            margin-bottom: 8px; 
            font-weight: 500; 
            color: #202124;
        }
        .form-group input, .form-group textarea { 
            width: 100%; 
            padding: 12px; 
            border: 1px solid #dadce0; 
            border-radius: 6px; 
            font-size: 14px;
            font-family: inherit;
            box-sizing: border-box;
        }
        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        .status { 
            padding: 16px; 
            border-radius: 8px; 
            margin: 16px 0; 
            font-weight: 500;
        }
        .status.success { 
            background: #e8f5e8; 
            color: #137333; 
            border: 1px solid #c8e6c9;
        }
        .status.error { 
            background: #fce8e6; 
            color: #d93025; 
            border: 1px solid #f8bbd9;
        }
        .status.info { 
            background: #e8f0fe; 
            color: #1a73e8; 
            border: 1px solid #aecbfa;
        }
        .grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
        }
        .server-info {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
            margin: 16px 0;
        }
        .server-info strong {
            color: #202124;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 MailSuite Email Server</h1>
            <p>help-coinmarcetcap.com • Professional Email Automation</p>
        </div>
        
        <div class="server-info">
            <strong>Server Status:</strong> VPS IP *************** • Domain: help-coinmarcetcap.com<br>
            <strong>Dashboard URL:</strong> http://***************:5000<br>
            <strong>Mail Server:</strong> mail.help-coinmarcetcap.com
        </div>
        
        <div class="stats">
            <div class="stat">
                <h3 id="queue-size">0</h3>
                <p>Queue Size</p>
            </div>
            <div class="stat">
                <h3 id="sent-count">0</h3>
                <p>Sent Today</p>
            </div>
            <div class="stat">
                <h3 id="failed-count">0</h3>
                <p>Failed</p>
            </div>
            <div class="stat">
                <h3 id="status">Stopped</h3>
                <p>Server Status</p>
            </div>
        </div>
        
        <div class="grid">
            <div class="section">
                <h3>🚀 Server Control</h3>
                <button class="button success" onclick="startServer()">Start Email Sender</button>
                <button class="button danger" onclick="stopServer()">Stop Email Sender</button>
                <button class="button" onclick="updateStatus()">Refresh Status</button>
                <button class="button" onclick="checkLogs()">View Logs</button>
            </div>
            
            <div class="section">
                <h3>📧 Send Test Email</h3>
                <div class="form-group">
                    <label>To Email:</label>
                    <input type="email" id="test-email" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label>Subject:</label>
                    <input type="text" id="test-subject" placeholder="Test from help-coinmarcetcap.com">
                </div>
                <div class="form-group">
                    <label>Message:</label>
                    <textarea id="test-message" rows="4" placeholder="Test message from MailSuite server"></textarea>
                </div>
                <button class="button" onclick="sendTestEmail()">Send Test Email</button>
            </div>
        </div>
        
        <div class="section">
            <h3>📊 Bulk Email Sending</h3>
            <div class="form-group">
                <label>Recipients (one email per line):</label>
                <textarea id="bulk-emails" rows="6" placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"></textarea>
            </div>
            <div class="form-group">
                <label>Subject:</label>
                <input type="text" id="bulk-subject" placeholder="Bulk email subject">
            </div>
            <div class="form-group">
                <label>Message:</label>
                <textarea id="bulk-message" rows="6" placeholder="Your bulk email message"></textarea>
            </div>
            <button class="button" onclick="sendBulkEmails()">Send Bulk Emails</button>
        </div>
        
        <div id="message-area"></div>
    </div>
    
    <script>
        function showMessage(message, type = 'success') {
            document.getElementById('message-area').innerHTML = 
                `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                document.getElementById('message-area').innerHTML = '';
            }, 5000);
        }
        
        function updateStatus() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('queue-size').textContent = data.queue_size;
                    document.getElementById('sent-count').textContent = data.sent_count;
                    document.getElementById('failed-count').textContent = data.failed_count;
                    document.getElementById('status').textContent = data.is_running ? 'Running' : 'Stopped';
                    document.getElementById('status').style.color = data.is_running ? '#34a853' : '#ea4335';
                })
                .catch(error => {
                    showMessage('Error updating status: ' + error.message, 'error');
                });
        }
        
        function startServer() {
            fetch('/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    showMessage('✅ Email sender started successfully!', 'success');
                    updateStatus();
                })
                .catch(error => {
                    showMessage('Error starting server: ' + error.message, 'error');
                });
        }
        
        function stopServer() {
            fetch('/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    showMessage('⏹️ Email sender stopped!', 'info');
                    updateStatus();
                })
                .catch(error => {
                    showMessage('Error stopping server: ' + error.message, 'error');
                });
        }
        
        function sendTestEmail() {
            const emailData = {
                to_email: document.getElementById('test-email').value,
                subject: document.getElementById('test-subject').value,
                body: document.getElementById('test-message').value
            };
            
            if (!emailData.to_email || !emailData.subject || !emailData.body) {
                showMessage('Please fill in all fields', 'error');
                return;
            }
            
            fetch('/send-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(emailData)
            })
            .then(response => response.json())
            .then(data => {
                showMessage('✅ Test email queued successfully!', 'success');
                updateStatus();
            })
            .catch(error => {
                showMessage('Error sending email: ' + error.message, 'error');
            });
        }
        
        function sendBulkEmails() {
            const emails = document.getElementById('bulk-emails').value.split('\\n').filter(e => e.trim());
            const subject = document.getElementById('bulk-subject').value;
            const message = document.getElementById('bulk-message').value;
            
            if (emails.length === 0 || !subject || !message) {
                showMessage('Please fill in all fields', 'error');
                return;
            }
            
            const bulkData = {
                emails: emails.map(email => ({
                    to_email: email.trim(),
                    subject: subject,
                    body: message
                }))
            };
            
            fetch('/send-bulk', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(bulkData)
            })
            .then(response => response.json())
            .then(data => {
                showMessage(`✅ ${data.emails_queued} emails queued for sending!`, 'success');
                updateStatus();
            })
            .catch(error => {
                showMessage('Error sending bulk emails: ' + error.message, 'error');
            });
        }
        
        function checkLogs() {
            showMessage('📋 Check server logs with: tail -f /var/log/mail.log', 'info');
        }
        
        // Auto-refresh every 5 seconds
        setInterval(updateStatus, 5000);
        updateStatus();
    </script>
</body>
</html>
'''

class EmailSender:
    def __init__(self):
        self.is_running = False
        
    def send_email(self, to_email, subject, body, from_email=None):
        global sent_count, failed_count
        
        if not from_email:
            from_email = DEFAULT_FROM_EMAIL
            
        try:
            msg = MIMEMultipart()
            msg['From'] = from_email
            msg['To'] = to_email
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'html'))
            
            server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
            server.send_message(msg)
            server.quit()
            
            sent_count += 1
            logging.info(f"✅ Email sent to {to_email}")
            return True
            
        except Exception as e:
            failed_count += 1
            logging.error(f"❌ Failed to send email to {to_email}: {e}")
            return False
    
    def process_queue(self):
        while self.is_running:
            try:
                email_data = email_queue.get(timeout=1)
                self.send_email(**email_data)
                time.sleep(DELAY_BETWEEN_EMAILS)
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Queue processing error: {e}")

sender = EmailSender()

@app.route('/')
def dashboard():
    return render_template_string(DASHBOARD_HTML)

@app.route('/send-email', methods=['POST'])
def send_email():
    data = request.json
    
    required_fields = ['to_email', 'subject', 'body']
    if not all(field in data for field in required_fields):
        return jsonify({'error': 'Missing required fields'}), 400
    
    email_queue.put({
        'to_email': data['to_email'],
        'subject': data['subject'],
        'body': data['body'],
        'from_email': data.get('from_email', DEFAULT_FROM_EMAIL)
    })
    
    return jsonify({'status': 'queued', 'queue_size': email_queue.qsize()})

@app.route('/send-bulk', methods=['POST'])
def send_bulk():
    data = request.json
    
    if 'emails' not in data:
        return jsonify({'error': 'No emails provided'}), 400
    
    count = 0
    for email_data in data['emails']:
        if all(field in email_data for field in ['to_email', 'subject', 'body']):
            email_queue.put(email_data)
            count += 1
    
    return jsonify({'status': 'queued', 'emails_queued': count, 'queue_size': email_queue.qsize()})

@app.route('/status', methods=['GET'])
def status():
    global sent_count, failed_count
    return jsonify({
        'queue_size': email_queue.qsize(),
        'is_running': sender.is_running,
        'sent_count': sent_count,
        'failed_count': failed_count,
        'domain': DOMAIN,
        'vps_ip': VPS_IP
    })

@app.route('/start', methods=['POST'])
def start_sender():
    if not sender.is_running:
        sender.is_running = True
        thread = threading.Thread(target=sender.process_queue)
        thread.daemon = True
        thread.start()
        return jsonify({'status': 'started'})
    return jsonify({'status': 'already_running'})

@app.route('/stop', methods=['POST'])
def stop_sender():
    sender.is_running = False
    return jsonify({'status': 'stopped'})

if __name__ == '__main__':
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('/opt/mailsuite-server/email.log'),
            logging.StreamHandler()
        ]
    )
    
    print(f"🚀 Starting MailSuite Email Server for {DOMAIN}")
    print(f"📧 Dashboard: http://{VPS_IP}:5000")
    print(f"📨 Mail server: mail.{DOMAIN}")
    
    app.run(host='0.0.0.0', port=5000, debug=False)
EOF

# Create systemd service
cat > /etc/systemd/system/mailsuite-server.service << EOF
[Unit]
Description=MailSuite Email Server for help-coinmarcetcap.com
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/mailsuite-server
ExecStart=/usr/bin/python3 /opt/mailsuite-server/server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Start services
echo -e "${BLUE}🚀 Starting services...${NC}"
systemctl enable opendkim
systemctl start opendkim
systemctl restart postfix
systemctl enable postfix

systemctl daemon-reload
systemctl enable mailsuite-server
systemctl start mailsuite-server

# Create DNS verification guide
cat > /opt/mailsuite-server/dns_verification.txt << EOF
=== DNS RECORDS FOR help-coinmarcetcap.com ===

Please verify these DNS records are configured:

1. A Record:
   mail.help-coinmarcetcap.com    A    ***************

2. MX Record:
   help-coinmarcetcap.com         MX   10 mail.help-coinmarcetcap.com

3. SPF Record:
   help-coinmarcetcap.com         TXT  "v=spf1 ip4:*************** ~all"

4. DMARC Record:
   _dmarc.help-coinmarcetcap.com  TXT  "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"

5. DKIM Record:
   default._domainkey.help-coinmarcetcap.com  TXT  "$(cat /etc/opendkim/keys/$DOMAIN/default.txt | grep -v '^default._domainkey' | tr -d '\n\t " ')"

6. PTR Record: ✅ Already configured

=== VERIFICATION COMMANDS ===
dig A mail.help-coinmarcetcap.com
dig MX help-coinmarcetcap.com
dig TXT help-coinmarcetcap.com
dig TXT _dmarc.help-coinmarcetcap.com

=== DASHBOARD ACCESS ===
http://***************:5000
EOF

# Final status check
echo ""
echo -e "${GREEN}🎉 INSTALLATION COMPLETE!${NC}"
echo ""
echo -e "${YELLOW}📋 Your MailSuite Server Details:${NC}"
echo "• Domain: help-coinmarcetcap.com"
echo "• VPS IP: ***************"
echo "• Mail Server: mail.help-coinmarcetcap.com"
echo "• Dashboard: http://***************:5000"
echo ""
echo -e "${YELLOW}🔍 Service Status:${NC}"
systemctl status postfix --no-pager -l | head -3
systemctl status mailsuite-server --no-pager -l | head -3
echo ""
echo -e "${YELLOW}🔐 DKIM Public Key (add to DNS):${NC}"
echo "Record: default._domainkey.help-coinmarcetcap.com"
echo "Type: TXT"
echo "Value:"
cat /etc/opendkim/keys/$DOMAIN/default.txt
echo ""
echo -e "${GREEN}✅ Next Steps:${NC}"
echo "1. Verify DNS records are configured"
echo "2. Access dashboard: http://***************:5000"
echo "3. Send test email from dashboard"
echo "4. Install Chrome extension"
echo ""
echo -e "${GREEN}🚀 Ready to send unlimited emails!${NC}"
