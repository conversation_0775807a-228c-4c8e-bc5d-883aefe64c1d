/* MailSuite Extension Styles */

/* Toolbar Button */
.mailsuite-toolbar-btn {
  margin-right: 8px !important;
}

.mailsuite-btn-text {
  font-size: 13px;
  color: #444;
  font-weight: 500;
}

.mailsuite-toolbar-btn:hover .mailsuite-btn-text {
  color: #1a73e8;
}

/* Compose Panel */
.mailsuite-compose-panel {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 8px;
  margin: 16px 0;
  font-family: 'Google Sans', Roboto, Arial, sans-serif;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.mailsuite-compose-panel.collapsed .mailsuite-panel-content {
  display: none;
}

.mailsuite-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #dadce0;
  border-radius: 8px 8px 0 0;
}

.mailsuite-panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #1a73e8;
  font-weight: 500;
}

.mailsuite-toggle-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #5f6368;
  padding: 4px 8px;
  border-radius: 4px;
}

.mailsuite-toggle-btn:hover {
  background: #f1f3f4;
}

.mailsuite-panel-content {
  padding: 16px;
}

/* Tabs */
.mailsuite-tabs {
  display: flex;
  border-bottom: 1px solid #dadce0;
  margin-bottom: 16px;
}

.mailsuite-tab {
  background: none;
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 13px;
  color: #5f6368;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.mailsuite-tab:hover {
  color: #1a73e8;
}

.mailsuite-tab.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
  font-weight: 500;
}

.mailsuite-tab-content {
  display: none;
}

.mailsuite-tab-content.active {
  display: block;
}

/* Sections and Fields */
.mailsuite-section {
  margin-bottom: 16px;
}

.mailsuite-section label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #202124;
}

.mailsuite-field {
  margin-bottom: 12px;
}

.mailsuite-field input[type="text"],
.mailsuite-field input[type="number"],
.mailsuite-field input[type="datetime-local"],
.mailsuite-field select,
.mailsuite-field textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 13px;
  font-family: inherit;
  box-sizing: border-box;
}

.mailsuite-field input:focus,
.mailsuite-field select:focus,
.mailsuite-field textarea:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.mailsuite-field textarea {
  height: 80px;
  resize: vertical;
}

/* File Upload */
.mailsuite-csv-upload {
  width: 100%;
  padding: 8px;
  border: 2px dashed #dadce0;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.mailsuite-csv-upload:hover {
  border-color: #1a73e8;
}

.mailsuite-contact-count {
  font-size: 12px;
  color: #5f6368;
  margin-top: 4px;
}

/* Variables */
.mailsuite-variables {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.mailsuite-var {
  background: #e8f0fe;
  color: #1a73e8;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #dadce0;
}

.mailsuite-var:hover {
  background: #1a73e8;
  color: white;
}

/* Buttons */
.mailsuite-btn-primary {
  background: #1a73e8;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mailsuite-btn-primary:hover {
  background: #1765cc;
}

.mailsuite-btn-secondary {
  background: #f8f9fa;
  color: #1a73e8;
  border: 1px solid #dadce0;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mailsuite-btn-secondary:hover {
  background: #e8f0fe;
}

/* Subdomain Settings */
.mailsuite-subdomain-settings {
  background: #f1f3f4;
  padding: 12px;
  border-radius: 4px;
  margin-top: 8px;
}

.mailsuite-subdomain-settings input,
.mailsuite-subdomain-settings textarea {
  margin-bottom: 8px;
}

/* Schedule Settings */
.mailsuite-schedule-settings {
  background: #f1f3f4;
  padding: 12px;
  border-radius: 4px;
  margin-top: 8px;
}

/* Modal */
.mailsuite-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mailsuite-modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.mailsuite-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #dadce0;
  background: #f8f9fa;
}

.mailsuite-modal-header h2 {
  margin: 0;
  font-size: 18px;
  color: #202124;
}

.mailsuite-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #5f6368;
  padding: 4px;
  border-radius: 4px;
}

.mailsuite-modal-close:hover {
  background: #f1f3f4;
}

/* Dashboard */
.mailsuite-dashboard-tabs {
  display: flex;
  border-bottom: 1px solid #dadce0;
  background: #f8f9fa;
}

.mailsuite-dash-tab {
  background: none;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  color: #5f6368;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.mailsuite-dash-tab:hover {
  color: #1a73e8;
  background: #e8f0fe;
}

.mailsuite-dash-tab.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
  background: white;
}

.mailsuite-dashboard-content {
  padding: 20px;
  max-height: 500px;
  overflow-y: auto;
}

.mailsuite-dash-content {
  display: none;
}

.mailsuite-dash-content.active {
  display: block;
}

/* Campaign Items */
.mailsuite-campaign-item {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 12px;
}

.mailsuite-campaign-item h4 {
  margin: 0 0 8px 0;
  color: #202124;
}

.mailsuite-campaign-item p {
  margin: 4px 0;
  font-size: 13px;
  color: #5f6368;
}

/* Template Items */
.mailsuite-template-item {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.mailsuite-template-item h4 {
  margin: 0 0 8px 0;
  color: #202124;
}

.mailsuite-template-item p {
  margin: 4px 0;
  font-size: 13px;
  color: #5f6368;
}

.mailsuite-template-item button {
  background: #ea4335;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

/* Analytics */
.mailsuite-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.mailsuite-stat {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 16px;
  text-align: center;
}

.mailsuite-stat h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #5f6368;
}

.mailsuite-stat p {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: #1a73e8;
}

/* Progress Indicator */
.mailsuite-progress {
  background: #e8f0fe;
  border: 1px solid #1a73e8;
  border-radius: 4px;
  padding: 12px;
  margin: 16px 0;
  display: none;
}

.mailsuite-progress.active {
  display: block;
}

.mailsuite-progress-bar {
  background: #dadce0;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  margin: 8px 0;
}

.mailsuite-progress-fill {
  background: #1a73e8;
  height: 100%;
  width: 0%;
  transition: width 0.3s ease;
}

.mailsuite-progress-text {
  font-size: 13px;
  color: #1a73e8;
  font-weight: 500;
}

/* Checkbox styling */
input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.1);
}

/* Loading spinner */
.mailsuite-loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1a73e8;
  border-radius: 50%;
  animation: mailsuite-spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes mailsuite-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 600px) {
  .mailsuite-modal-content {
    width: 95%;
    margin: 20px;
  }

  .mailsuite-tabs {
    flex-wrap: wrap;
  }

  .mailsuite-tab {
    flex: 1;
    min-width: 80px;
  }

  .mailsuite-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
