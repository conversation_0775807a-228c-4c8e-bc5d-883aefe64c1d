@echo off
echo 🚀 Excel to CSV Splitter
echo ========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Install required packages
echo 📦 Installing required packages...
pip install pandas openpyxl xlrd

REM Run the Python script
echo.
echo 🔄 Processing Excel file...
python split_excel_to_csv.py

echo.
echo ✅ Process completed!
pause
